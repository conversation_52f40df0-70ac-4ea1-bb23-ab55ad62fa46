"""
限流中间件
"""

from fastapi import Request, Response
from fastapi.middleware.base import BaseHTTPMiddleware
from fastapi.responses import JSONResponse
import time
from loguru import logger

from ..core.redis import get_redis
from ..core.config import settings


class RateLimitMiddleware(BaseHTTPMiddleware):
    """限流中间件"""
    
    def __init__(self, app):
        super().__init__(app)
        self.redis = get_redis()
        self.enabled = settings.RATE_LIMIT_ENABLED
        self.default_limit = settings.RATE_LIMIT_REQUESTS
        self.default_window = settings.RATE_LIMIT_WINDOW
        
        # 不同路径的限流配置
        self.rate_limits = {
            "/api/v1/ai/chat": {"requests": 20, "window": 60},
            "/api/v1/ai/knowledge": {"requests": 50, "window": 60},
            "/api/v1/auth/login": {"requests": 5, "window": 300},  # 5分钟内最多5次登录
            "/api/v1/files/upload": {"requests": 10, "window": 60},
        }
    
    def get_client_identifier(self, request: Request) -> str:
        """获取客户端标识符"""
        # 优先使用用户ID（如果已认证）
        if hasattr(request.state, 'user') and request.state.user:
            return f"user:{request.state.user['user_id']}"
        
        # 使用IP地址
        client_ip = request.client.host if request.client else "unknown"
        
        # 检查是否有代理IP
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            client_ip = forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            client_ip = real_ip
        
        return f"ip:{client_ip}"
    
    def get_rate_limit_config(self, path: str) -> dict:
        """获取路径的限流配置"""
        # 检查精确匹配
        if path in self.rate_limits:
            return self.rate_limits[path]
        
        # 检查前缀匹配
        for pattern, config in self.rate_limits.items():
            if path.startswith(pattern):
                return config
        
        # 返回默认配置
        return {"requests": self.default_limit, "window": self.default_window}
    
    async def dispatch(self, request: Request, call_next):
        """处理请求"""
        
        if not self.enabled:
            return await call_next(request)
        
        try:
            # 获取客户端标识符
            client_id = self.get_client_identifier(request)
            
            # 获取限流配置
            rate_config = self.get_rate_limit_config(request.url.path)
            limit = rate_config["requests"]
            window = rate_config["window"]
            
            # 构建Redis键
            rate_limit_key = f"rate_limit:{client_id}:{request.url.path}"
            
            # 检查限流
            rate_info = await self.redis.get_rate_limit_info(rate_limit_key, window, limit)
            
            if not rate_info["allowed"]:
                # 超出限流
                logger.warning(
                    f"Rate limit exceeded for {client_id} on {request.url.path}"
                )
                
                return JSONResponse(
                    status_code=429,
                    content={
                        "error": "Rate limit exceeded",
                        "limit": limit,
                        "window": window,
                        "reset_time": rate_info["reset_time"]
                    },
                    headers={
                        "X-RateLimit-Limit": str(limit),
                        "X-RateLimit-Remaining": str(rate_info["remaining"]),
                        "X-RateLimit-Reset": str(rate_info["reset_time"]),
                        "Retry-After": str(window)
                    }
                )
            
            # 处理请求
            response = await call_next(request)
            
            # 添加限流信息到响应头
            response.headers["X-RateLimit-Limit"] = str(limit)
            response.headers["X-RateLimit-Remaining"] = str(rate_info["remaining"])
            response.headers["X-RateLimit-Reset"] = str(rate_info["reset_time"])
            
            return response
            
        except Exception as e:
            logger.error(f"Rate limiting error: {e}")
            # 限流服务出错时，允许请求通过
            return await call_next(request)
