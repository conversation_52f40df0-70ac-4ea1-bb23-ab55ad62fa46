"""
网关管理API
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Dict, List, Any, Optional
from loguru import logger

from ...core.config import settings
from ...core.redis import get_redis
from ...core.auth import get_auth

router = APIRouter()


class ServiceStatus(BaseModel):
    """服务状态"""
    name: str
    url: str
    healthy: bool
    last_check: Optional[float] = None
    response_time: Optional[float] = None


class GatewayStats(BaseModel):
    """网关统计"""
    total_requests: int
    successful_requests: int
    failed_requests: int
    average_response_time: float
    active_connections: int


class RouteConfig(BaseModel):
    """路由配置"""
    path: str
    service: str
    methods: List[str] = ["GET", "POST", "PUT", "DELETE"]
    auth_required: bool = True
    rate_limit: Optional[Dict[str, int]] = None


@router.get("/services", response_model=List[ServiceStatus])
async def get_services_status():
    """获取所有服务状态"""
    try:
        services = []
        
        for service_name, service_config in settings.BACKEND_SERVICES.items():
            # 这里应该从健康检查中获取实际状态
            # 暂时返回模拟数据
            services.append(ServiceStatus(
                name=service_name,
                url=service_config["url"],
                healthy=True,  # 实际应该从健康检查获取
                last_check=None,
                response_time=None
            ))
        
        return services
        
    except Exception as e:
        logger.error(f"Failed to get services status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/stats", response_model=GatewayStats)
async def get_gateway_stats(
    redis = Depends(get_redis)
):
    """获取网关统计信息"""
    try:
        # 从Redis获取统计信息
        total_requests = await redis.get("gateway:stats:total_requests") or 0
        successful_requests = await redis.get("gateway:stats:successful_requests") or 0
        failed_requests = await redis.get("gateway:stats:failed_requests") or 0
        
        # 计算平均响应时间
        total_response_time = await redis.get("gateway:stats:total_response_time") or 0
        average_response_time = 0
        if total_requests > 0:
            average_response_time = total_response_time / total_requests
        
        return GatewayStats(
            total_requests=total_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            average_response_time=average_response_time,
            active_connections=0  # 实际应该从连接池获取
        )
        
    except Exception as e:
        logger.error(f"Failed to get gateway stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/routes")
async def get_routes():
    """获取路由配置"""
    try:
        routes = []
        
        for path, service in settings.ROUTE_MAPPINGS.items():
            routes.append(RouteConfig(
                path=path,
                service=service,
                methods=["GET", "POST", "PUT", "DELETE"],
                auth_required=True
            ))
        
        return routes
        
    except Exception as e:
        logger.error(f"Failed to get routes: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/routes")
async def add_route(
    route: RouteConfig,
    redis = Depends(get_redis)
):
    """添加路由配置"""
    try:
        # 将路由配置保存到Redis
        route_key = f"gateway:routes:{route.path}"
        route_data = route.dict()
        
        await redis.set(route_key, route_data, ttl=None)
        
        logger.info(f"Added route: {route.path} -> {route.service}")
        return {"message": "Route added successfully"}
        
    except Exception as e:
        logger.error(f"Failed to add route: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/routes/{path:path}")
async def delete_route(
    path: str,
    redis = Depends(get_redis)
):
    """删除路由配置"""
    try:
        route_key = f"gateway:routes:/{path}"
        deleted = await redis.delete(route_key)
        
        if deleted:
            logger.info(f"Deleted route: /{path}")
            return {"message": "Route deleted successfully"}
        else:
            raise HTTPException(status_code=404, detail="Route not found")
        
    except Exception as e:
        logger.error(f"Failed to delete route: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/cache/clear")
async def clear_cache(
    redis = Depends(get_redis)
):
    """清除缓存"""
    try:
        # 清除所有网关相关的缓存
        keys_pattern = "gateway:*"
        
        # 这里应该使用Redis的SCAN命令来安全地删除键
        # 暂时简化处理
        await redis.redis_client.flushdb()
        
        logger.info("Gateway cache cleared")
        return {"message": "Cache cleared successfully"}
        
    except Exception as e:
        logger.error(f"Failed to clear cache: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/config")
async def get_gateway_config():
    """获取网关配置"""
    try:
        config = {
            "rate_limit": {
                "enabled": settings.RATE_LIMIT_ENABLED,
                "requests": settings.RATE_LIMIT_REQUESTS,
                "window": settings.RATE_LIMIT_WINDOW
            },
            "load_balancer": {
                "algorithm": settings.LOAD_BALANCER_ALGORITHM,
                "health_check_interval": settings.HEALTH_CHECK_INTERVAL
            },
            "cache": {
                "enabled": settings.CACHE_ENABLED,
                "ttl": settings.CACHE_TTL
            },
            "cors": {
                "origins": settings.CORS_ORIGINS
            }
        }
        
        return config
        
    except Exception as e:
        logger.error(f"Failed to get gateway config: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def gateway_health():
    """网关健康检查"""
    return {"status": "healthy", "service": "gateway"}
