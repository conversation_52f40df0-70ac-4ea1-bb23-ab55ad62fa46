"""
线索洞察智能体
"""

from typing import Dict, Any, List, Optional
import asyncio
from datetime import datetime, timedelta
from loguru import logger

from .base_agent import BaseAgent, AgentTask, TaskPriority
from ..services.lead_service import LeadService
from ..ai.lead_scoring import LeadScoringEngine
from ..core.database import get_db_session
from ..core.redis import get_redis
from ..core.mongodb import get_mongodb


class LeadInsightAgent(BaseAgent):
    """线索洞察智能体"""
    
    def __init__(self):
        super().__init__(
            agent_id="lead-insight-agent",
            agent_name="线索洞察智能体"
        )
        
        self.lead_service: Optional[LeadService] = None
        self.scoring_engine: Optional[LeadScoringEngine] = None
        
        # 注册专门的任务处理器
        self._register_lead_handlers()
    
    def _register_lead_handlers(self):
        """注册线索相关任务处理器"""
        self.task_handlers.update({
            "score_lead": self._handle_score_lead,
            "analyze_leads": self._handle_analyze_leads,
            "auto_assign_leads": self._handle_auto_assign_leads,
            "follow_up_reminder": self._handle_follow_up_reminder,
            "lead_quality_check": self._handle_lead_quality_check,
            "competitor_analysis": self._handle_competitor_analysis,
            "conversion_prediction": self._handle_conversion_prediction,
            "lead_nurturing": self._handle_lead_nurturing
        })
    
    async def initialize(self):
        """初始化智能体"""
        try:
            # 初始化服务
            db_session = await get_db_session()
            redis_client = get_redis()
            mongodb_client = get_mongodb()
            
            self.lead_service = LeadService(db_session, redis_client, mongodb_client)
            self.scoring_engine = LeadScoringEngine()
            
            # 设置初始上下文
            self.context.update({
                "initialized_at": datetime.utcnow().isoformat(),
                "auto_scoring_enabled": True,
                "auto_assignment_enabled": True,
                "follow_up_enabled": True,
                "learning_mode": True
            })
            
            # 启动定时任务
            asyncio.create_task(self._start_periodic_tasks())
            
            logger.info("Lead Insight Agent initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Lead Insight Agent: {e}")
            raise
    
    async def _start_periodic_tasks(self):
        """启动定时任务"""
        # 每小时分析线索
        asyncio.create_task(self._periodic_lead_analysis())
        
        # 每天检查跟进提醒
        asyncio.create_task(self._periodic_follow_up_check())
        
        # 每天进行质量检查
        asyncio.create_task(self._periodic_quality_check())
    
    async def _periodic_lead_analysis(self):
        """定期线索分析"""
        while self.is_running:
            try:
                if self.context.get("auto_scoring_enabled", True):
                    task = AgentTask(
                        task_type="analyze_leads",
                        data={"type": "periodic"},
                        priority=TaskPriority.NORMAL
                    )
                    await self.add_task(task)
                
                # 每小时执行一次
                await asyncio.sleep(3600)
                
            except Exception as e:
                logger.error(f"Error in periodic lead analysis: {e}")
                await asyncio.sleep(300)  # 出错时5分钟后重试
    
    async def _periodic_follow_up_check(self):
        """定期跟进检查"""
        while self.is_running:
            try:
                if self.context.get("follow_up_enabled", True):
                    task = AgentTask(
                        task_type="follow_up_reminder",
                        data={"type": "periodic"},
                        priority=TaskPriority.HIGH
                    )
                    await self.add_task(task)
                
                # 每天执行一次
                await asyncio.sleep(86400)
                
            except Exception as e:
                logger.error(f"Error in periodic follow-up check: {e}")
                await asyncio.sleep(3600)
    
    async def _periodic_quality_check(self):
        """定期质量检查"""
        while self.is_running:
            try:
                task = AgentTask(
                    task_type="lead_quality_check",
                    data={"type": "periodic"},
                    priority=TaskPriority.NORMAL
                )
                await self.add_task(task)
                
                # 每天执行一次
                await asyncio.sleep(86400)
                
            except Exception as e:
                logger.error(f"Error in periodic quality check: {e}")
                await asyncio.sleep(3600)
    
    async def process_task(self, task: AgentTask) -> Dict[str, Any]:
        """处理通用任务"""
        logger.warning(f"No specific handler for task type: {task.task_type}")
        return {"message": f"Task {task.task_type} processed with default handler"}
    
    async def _handle_score_lead(self, task: AgentTask) -> Dict[str, Any]:
        """处理线索评分任务"""
        try:
            lead_id = task.data.get("lead_id")
            if not lead_id:
                raise ValueError("lead_id is required for score_lead task")
            
            # 获取线索信息
            lead = await self.lead_service.get_lead_by_id(lead_id)
            if not lead:
                raise ValueError(f"Lead {lead_id} not found")
            
            # 获取客户信息
            customer = await self.lead_service.get_customer_by_id(lead.customer_id)
            
            # 获取活动记录
            activities = await self.lead_service.get_lead_activities(lead_id)
            
            # 执行评分
            scoring_result = await self.scoring_engine.score_lead(lead, customer, activities)
            
            # 更新线索评分
            await self.lead_service.update_lead_score(lead_id, scoring_result["score"])
            
            return {
                "lead_id": lead_id,
                "score": scoring_result["score"],
                "quality_score": scoring_result["quality_score"],
                "conversion_probability": scoring_result["conversion_probability"],
                "recommendations": scoring_result["recommendations"]
            }
            
        except Exception as e:
            logger.error(f"Error scoring lead: {e}")
            raise
    
    async def _handle_analyze_leads(self, task: AgentTask) -> Dict[str, Any]:
        """处理线索分析任务"""
        try:
            # 获取需要分析的线索
            leads = await self.lead_service.get_leads_for_analysis()
            
            analyzed_count = 0
            high_potential_leads = []
            
            for lead in leads:
                # 为每个线索创建评分任务
                score_task = AgentTask(
                    task_type="score_lead",
                    data={"lead_id": lead.id},
                    priority=TaskPriority.NORMAL
                )
                await self.add_task(score_task)
                analyzed_count += 1
                
                # 识别高潜力线索
                if lead.score and lead.score >= 80:
                    high_potential_leads.append({
                        "id": lead.id,
                        "name": lead.name,
                        "score": lead.score
                    })
            
            return {
                "analyzed_count": analyzed_count,
                "high_potential_leads": high_potential_leads,
                "analysis_type": task.data.get("type", "manual")
            }
            
        except Exception as e:
            logger.error(f"Error analyzing leads: {e}")
            raise
    
    async def _handle_auto_assign_leads(self, task: AgentTask) -> Dict[str, Any]:
        """处理自动分配线索任务"""
        try:
            if not self.context.get("auto_assignment_enabled", True):
                return {"message": "Auto assignment is disabled"}
            
            # 获取未分配的线索
            unassigned_leads = await self.lead_service.get_unassigned_leads()
            
            assigned_count = 0
            assignments = []
            
            for lead in unassigned_leads:
                # 智能分配逻辑
                assigned_user = await self._decide_lead_assignment(lead)
                
                if assigned_user:
                    await self.lead_service.assign_lead(lead.id, assigned_user["user_id"])
                    assigned_count += 1
                    
                    assignments.append({
                        "lead_id": lead.id,
                        "assigned_to": assigned_user["username"],
                        "reason": assigned_user["reason"]
                    })
            
            return {
                "assigned_count": assigned_count,
                "assignments": assignments
            }
            
        except Exception as e:
            logger.error(f"Error in auto assignment: {e}")
            raise
    
    async def _handle_follow_up_reminder(self, task: AgentTask) -> Dict[str, Any]:
        """处理跟进提醒任务"""
        try:
            # 获取需要跟进的线索
            leads_to_follow = await self.lead_service.get_leads_needing_follow_up()
            
            reminders_sent = 0
            
            for lead in leads_to_follow:
                # 创建跟进提醒
                reminder_data = {
                    "lead_id": lead.id,
                    "assigned_to": lead.assigned_to,
                    "last_contact": lead.last_contact_date,
                    "urgency": self._calculate_follow_up_urgency(lead)
                }
                
                # 发送提醒（这里应该调用通知服务）
                await self._send_follow_up_reminder(reminder_data)
                reminders_sent += 1
            
            return {
                "reminders_sent": reminders_sent,
                "reminder_type": task.data.get("type", "manual")
            }
            
        except Exception as e:
            logger.error(f"Error in follow-up reminder: {e}")
            raise
    
    async def _handle_lead_quality_check(self, task: AgentTask) -> Dict[str, Any]:
        """处理线索质量检查任务"""
        try:
            # 获取最近的线索
            recent_leads = await self.lead_service.get_recent_leads(days=7)
            
            quality_issues = []
            checked_count = 0
            
            for lead in recent_leads:
                issues = await self._check_lead_quality(lead)
                if issues:
                    quality_issues.append({
                        "lead_id": lead.id,
                        "issues": issues
                    })
                checked_count += 1
            
            return {
                "checked_count": checked_count,
                "quality_issues_count": len(quality_issues),
                "quality_issues": quality_issues
            }
            
        except Exception as e:
            logger.error(f"Error in quality check: {e}")
            raise
    
    async def _handle_competitor_analysis(self, task: AgentTask) -> Dict[str, Any]:
        """处理竞争对手分析任务"""
        try:
            lead_id = task.data.get("lead_id")
            
            # 分析竞争情况
            competitor_info = await self._analyze_competitors(lead_id)
            
            return {
                "lead_id": lead_id,
                "competitors": competitor_info,
                "competitive_advantage": await self._identify_competitive_advantage(competitor_info)
            }
            
        except Exception as e:
            logger.error(f"Error in competitor analysis: {e}")
            raise
    
    async def _handle_conversion_prediction(self, task: AgentTask) -> Dict[str, Any]:
        """处理转化预测任务"""
        try:
            lead_id = task.data.get("lead_id")
            
            # 预测转化概率
            prediction = await self._predict_conversion(lead_id)
            
            return {
                "lead_id": lead_id,
                "conversion_probability": prediction["probability"],
                "predicted_timeline": prediction["timeline"],
                "key_factors": prediction["factors"]
            }
            
        except Exception as e:
            logger.error(f"Error in conversion prediction: {e}")
            raise
    
    async def _handle_lead_nurturing(self, task: AgentTask) -> Dict[str, Any]:
        """处理线索培育任务"""
        try:
            lead_id = task.data.get("lead_id")
            
            # 制定培育策略
            nurturing_plan = await self._create_nurturing_plan(lead_id)
            
            return {
                "lead_id": lead_id,
                "nurturing_plan": nurturing_plan,
                "next_actions": nurturing_plan.get("next_actions", [])
            }
            
        except Exception as e:
            logger.error(f"Error in lead nurturing: {e}")
            raise
    
    async def make_decision(self, situation: Dict[str, Any]) -> Dict[str, Any]:
        """智能决策"""
        try:
            decision_type = situation.get("type")
            
            if decision_type == "lead_assignment":
                return await self._decide_lead_assignment(situation.get("lead"))
            elif decision_type == "follow_up_timing":
                return await self._decide_follow_up_timing(situation.get("lead"))
            elif decision_type == "nurturing_strategy":
                return await self._decide_nurturing_strategy(situation.get("lead"))
            else:
                return await super().make_decision(situation)
                
        except Exception as e:
            logger.error(f"Error in decision making: {e}")
            return {
                "decision": "error",
                "confidence": 0.0,
                "reasoning": f"Decision making failed: {str(e)}"
            }
    
    # 辅助方法
    async def _decide_lead_assignment(self, lead) -> Optional[Dict[str, Any]]:
        """决定线索分配"""
        # 简化的分配逻辑
        # 实际应该考虑销售人员的负载、专业领域、历史表现等
        return {
            "user_id": "default-user",
            "username": "默认销售",
            "reason": "基于负载均衡的自动分配"
        }
    
    def _calculate_follow_up_urgency(self, lead) -> str:
        """计算跟进紧急程度"""
        if not lead.last_contact_date:
            return "high"
        
        days_since_contact = (datetime.utcnow() - lead.last_contact_date).days
        
        if days_since_contact > 7:
            return "high"
        elif days_since_contact > 3:
            return "medium"
        else:
            return "low"
    
    async def _send_follow_up_reminder(self, reminder_data: Dict[str, Any]):
        """发送跟进提醒"""
        # 这里应该调用通知服务
        logger.info(f"Follow-up reminder sent for lead {reminder_data['lead_id']}")
    
    async def _check_lead_quality(self, lead) -> List[str]:
        """检查线索质量"""
        issues = []
        
        if not lead.phone and not lead.email:
            issues.append("缺少联系方式")
        
        if not lead.company:
            issues.append("缺少公司信息")
        
        if lead.score and lead.score < 30:
            issues.append("评分过低")
        
        return issues
    
    async def _analyze_competitors(self, lead_id: str) -> List[Dict[str, Any]]:
        """分析竞争对手"""
        # 简化实现
        return [
            {"name": "竞争对手A", "strength": "价格优势"},
            {"name": "竞争对手B", "strength": "技术领先"}
        ]
    
    async def _identify_competitive_advantage(self, competitor_info: List[Dict[str, Any]]) -> List[str]:
        """识别竞争优势"""
        return ["服务质量", "客户关系", "定制化能力"]
    
    async def _predict_conversion(self, lead_id: str) -> Dict[str, Any]:
        """预测转化"""
        return {
            "probability": 0.65,
            "timeline": "30-45天",
            "factors": ["预算匹配", "决策时间线明确", "需求强烈"]
        }
    
    async def _create_nurturing_plan(self, lead_id: str) -> Dict[str, Any]:
        """创建培育计划"""
        return {
            "strategy": "教育式营销",
            "duration": "60天",
            "next_actions": [
                "发送产品介绍资料",
                "安排产品演示",
                "邀请参加网络研讨会"
            ]
        }
    
    async def _decide_follow_up_timing(self, lead) -> Dict[str, Any]:
        """决定跟进时机"""
        return {
            "decision": "3天后跟进",
            "confidence": 0.8,
            "reasoning": "基于客户行为分析的最佳跟进时机"
        }
    
    async def _decide_nurturing_strategy(self, lead) -> Dict[str, Any]:
        """决定培育策略"""
        return {
            "decision": "教育式培育",
            "confidence": 0.7,
            "reasoning": "客户处于信息收集阶段，需要教育式内容"
        }
