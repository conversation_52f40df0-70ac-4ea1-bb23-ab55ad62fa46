"""
API v1 路由模块
"""

from fastapi import APIRouter

from .leads import router as leads_router
from .customers import router as customers_router
from .activities import router as activities_router
from .analytics import router as analytics_router
from .agent import router as agent_router

# 创建API路由器
api_router = APIRouter()

# 注册子路由
api_router.include_router(leads_router, prefix="/leads", tags=["线索管理"])
api_router.include_router(customers_router, prefix="/customers", tags=["客户管理"])
api_router.include_router(activities_router, prefix="/activities", tags=["活动管理"])
api_router.include_router(analytics_router, prefix="/analytics", tags=["分析报表"])
api_router.include_router(agent_router, prefix="/agent", tags=["智能体管理"])