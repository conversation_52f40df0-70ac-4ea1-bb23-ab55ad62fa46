# MCP Gateway Service - API网关服务

MCP网关服务是整个智能体系统的统一入口，提供认证、路由、限流、负载均衡等核心功能。

## 🎯 核心功能

### 统一认证
- **JWT令牌认证** - 支持访问令牌和刷新令牌
- **API密钥认证** - 支持第三方系统集成
- **权限控制** - 基于角色的访问控制（RBAC）
- **令牌管理** - 令牌黑名单和过期管理

### 智能路由
- **路径匹配** - 基于URL路径的服务路由
- **动态路由** - 支持运行时路由配置
- **服务发现** - 支持静态、Consul、etcd服务发现
- **健康检查** - 自动检测后端服务健康状态

### 限流保护
- **滑动窗口限流** - 基于Redis的分布式限流
- **分级限流** - 不同API路径的差异化限流策略
- **用户级限流** - 基于用户身份的个性化限流
- **IP限流** - 基于客户端IP的访问控制

### 负载均衡
- **轮询算法** - 支持轮询、最少连接等算法
- **健康检查** - 自动剔除不健康的服务实例
- **故障转移** - 自动切换到健康的服务实例
- **超时重试** - 请求超时和重试机制

## 🏗️ 技术架构

```
网关服务架构
├── 中间件层
│   ├── 日志中间件 - 请求日志记录
│   ├── 限流中间件 - 访问频率控制
│   ├── 认证中间件 - 身份验证和授权
│   └── 负载均衡中间件 - 请求转发和负载均衡
├── API层 (FastAPI)
│   ├── 网关管理API
│   └── 监控管理API
├── 核心服务层
│   ├── 认证管理器 - JWT和API密钥管理
│   ├── Redis管理器 - 缓存和限流存储
│   └── 服务发现管理器 - 后端服务管理
└── 存储层
    ├── Redis - 缓存、限流、会话
    └── 配置文件 - 静态服务配置
```

## 🚀 快速开始

### 环境要求
- Python 3.11+
- Redis 7+
- Docker & Docker Compose

### 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
SECRET_KEY=your-secret-key-here
REDIS_URL=redis://redis:6379
CORS_ORIGINS=http://localhost:3000,http://localhost:3001
```

### 启动服务
```bash
# 使用Docker启动
docker-compose up mcp-gateway

# 或本地开发启动
pip install -r requirements.txt
python main.py
```

### API文档
启动后访问：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 📚 使用示例

### 认证请求
```bash
# 获取访问令牌（通过MCP Core服务）
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "password"}'

# 使用令牌访问API
curl -X GET "http://localhost:8000/api/v1/leads" \
  -H "Authorization: Bearer your-access-token"

# 使用API密钥访问
curl -X GET "http://localhost:8000/api/v1/leads" \
  -H "X-API-Key: your-api-key"
```

### 路由配置
```python
import requests

# 添加新路由
response = requests.post("http://localhost:8000/api/v1/gateway/routes", json={
    "path": "/api/v1/custom",
    "service": "custom-service",
    "methods": ["GET", "POST"],
    "auth_required": True,
    "rate_limit": {"requests": 100, "window": 60}
})
```

### 监控查询
```bash
# 获取网关统计
curl "http://localhost:8000/api/v1/monitoring/metrics"

# 获取服务健康状态
curl "http://localhost:8000/api/v1/monitoring/health/services"

# 获取网关配置
curl "http://localhost:8000/api/v1/gateway/config"
```

## 🔧 配置说明

### 主要配置项
- `SECRET_KEY`: JWT签名密钥
- `REDIS_URL`: Redis连接地址
- `RATE_LIMIT_REQUESTS`: 默认限流请求数
- `RATE_LIMIT_WINDOW`: 限流时间窗口（秒）
- `LOAD_BALANCER_ALGORITHM`: 负载均衡算法

### 服务配置
```python
BACKEND_SERVICES = {
    "service-name": {
        "url": "http://service:port",
        "health_check": "/health",
        "timeout": 30,
        "retry": 3
    }
}
```

### 路由映射
```python
ROUTE_MAPPINGS = {
    "/api/v1/leads": "lead-insight",
    "/api/v1/ai": "ai-service",
    "/api/v1/auth": "mcp-core"
}
```

## 🔍 监控和日志

### 健康检查
```bash
curl http://localhost:8000/health
```

### 指标监控
- 请求总数和成功率
- 平均响应时间
- 服务健康状态
- 限流统计

### 日志格式
```json
{
  "request_id": "uuid",
  "method": "GET",
  "path": "/api/v1/leads",
  "status_code": 200,
  "process_time": 0.123,
  "client_ip": "***********",
  "user_id": "user-uuid"
}
```

## 🛠️ 开发指南

### 添加新中间件
1. 在 `app/middleware/` 下创建中间件文件
2. 继承 `BaseHTTPMiddleware`
3. 在 `main.py` 中注册中间件

### 自定义认证策略
1. 在 `app/core/auth.py` 中添加认证方法
2. 在认证中间件中调用新方法
3. 更新配置支持新策略

### 扩展服务发现
1. 在 `app/core/service_discovery.py` 中添加新的发现类型
2. 实现服务注册和监控方法
3. 更新配置支持新类型

## 🔐 安全考虑

- JWT密钥安全存储
- API密钥定期轮换
- 请求频率限制
- IP白名单控制
- 敏感信息脱敏

## 📈 性能优化

- Redis连接池
- 异步请求处理
- 响应缓存机制
- 连接复用
- 健康检查优化

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 📄 许可证

MIT License
