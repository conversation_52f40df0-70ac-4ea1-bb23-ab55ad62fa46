"""
监控管理API
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Dict, List, Any, Optional
import time
from loguru import logger

from ...core.redis import get_redis

router = APIRouter()


class MetricData(BaseModel):
    """指标数据"""
    name: str
    value: float
    timestamp: float
    labels: Optional[Dict[str, str]] = {}


class HealthStatus(BaseModel):
    """健康状态"""
    service: str
    status: str
    timestamp: float
    details: Optional[Dict[str, Any]] = {}


@router.get("/metrics")
async def get_metrics(
    redis = Depends(get_redis)
):
    """获取监控指标"""
    try:
        metrics = []
        
        # 获取请求统计
        total_requests = await redis.get("gateway:stats:total_requests") or 0
        successful_requests = await redis.get("gateway:stats:successful_requests") or 0
        failed_requests = await redis.get("gateway:stats:failed_requests") or 0
        
        current_time = time.time()
        
        metrics.extend([
            MetricData(
                name="gateway_requests_total",
                value=total_requests,
                timestamp=current_time,
                labels={"type": "total"}
            ),
            MetricData(
                name="gateway_requests_total",
                value=successful_requests,
                timestamp=current_time,
                labels={"type": "successful"}
            ),
            MetricData(
                name="gateway_requests_total",
                value=failed_requests,
                timestamp=current_time,
                labels={"type": "failed"}
            )
        ])
        
        return metrics
        
    except Exception as e:
        logger.error(f"Failed to get metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health/services")
async def get_services_health():
    """获取所有服务健康状态"""
    try:
        # 这里应该从实际的健康检查获取数据
        # 暂时返回模拟数据
        services_health = []
        
        from ...core.config import settings
        
        for service_name in settings.BACKEND_SERVICES.keys():
            services_health.append(HealthStatus(
                service=service_name,
                status="healthy",
                timestamp=time.time(),
                details={"response_time": 0.1}
            ))
        
        return services_health
        
    except Exception as e:
        logger.error(f"Failed to get services health: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/logs")
async def get_logs(
    limit: int = 100,
    level: str = "INFO"
):
    """获取日志"""
    try:
        # 这里应该从日志系统获取实际日志
        # 暂时返回模拟数据
        logs = [
            {
                "timestamp": time.time(),
                "level": "INFO",
                "message": "Gateway service started",
                "service": "mcp-gateway"
            }
        ]
        
        return logs
        
    except Exception as e:
        logger.error(f"Failed to get logs: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def monitoring_health():
    """监控服务健康检查"""
    return {"status": "healthy", "service": "monitoring"}
