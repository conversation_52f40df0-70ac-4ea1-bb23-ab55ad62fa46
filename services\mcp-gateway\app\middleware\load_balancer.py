"""
负载均衡中间件
"""

from fastapi import Request, Response, HTTPException
from fastapi.middleware.base import BaseHTTPMiddleware
from fastapi.responses import JSONResponse
import httpx
import asyncio
import time
from typing import Dict, List, Optional
from loguru import logger

from ..core.config import settings, get_service_for_path, get_service_url
from ..core.redis import get_redis


class LoadBalancerMiddleware(BaseHTTPMiddleware):
    """负载均衡中间件"""
    
    def __init__(self, app):
        super().__init__(app)
        self.redis = get_redis()
        self.service_health = {}
        self.last_health_check = {}
        self.round_robin_counters = {}
        
        # 启动健康检查任务
        asyncio.create_task(self.health_check_loop())
    
    async def health_check_loop(self):
        """健康检查循环"""
        while True:
            try:
                await self.check_all_services_health()
                await asyncio.sleep(settings.HEALTH_CHECK_INTERVAL)
            except Exception as e:
                logger.error(f"Health check error: {e}")
                await asyncio.sleep(10)
    
    async def check_service_health(self, service_name: str, service_config: dict) -> bool:
        """检查单个服务健康状态"""
        try:
            url = service_config["url"]
            health_path = service_config.get("health_check", "/health")
            timeout = service_config.get("timeout", 5)
            
            async with httpx.AsyncClient(timeout=timeout) as client:
                response = await client.get(f"{url}{health_path}")
                is_healthy = response.status_code == 200
                
                # 更新健康状态
                self.service_health[service_name] = is_healthy
                self.last_health_check[service_name] = time.time()
                
                if is_healthy:
                    logger.debug(f"Service {service_name} is healthy")
                else:
                    logger.warning(f"Service {service_name} health check failed: {response.status_code}")
                
                return is_healthy
                
        except Exception as e:
            logger.error(f"Health check failed for {service_name}: {e}")
            self.service_health[service_name] = False
            self.last_health_check[service_name] = time.time()
            return False
    
    async def check_all_services_health(self):
        """检查所有服务健康状态"""
        tasks = []
        for service_name, service_config in settings.BACKEND_SERVICES.items():
            task = self.check_service_health(service_name, service_config)
            tasks.append(task)
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    
    def get_healthy_services(self, service_name: str) -> List[str]:
        """获取健康的服务实例"""
        # 简化版本：只返回单个服务
        # 在实际部署中，这里应该返回多个服务实例
        if self.service_health.get(service_name, True):  # 默认认为健康
            return [service_name]
        return []
    
    def select_service_instance(self, service_name: str) -> Optional[str]:
        """选择服务实例"""
        healthy_services = self.get_healthy_services(service_name)
        
        if not healthy_services:
            return None
        
        # 轮询算法
        if settings.LOAD_BALANCER_ALGORITHM == "round_robin":
            if service_name not in self.round_robin_counters:
                self.round_robin_counters[service_name] = 0
            
            index = self.round_robin_counters[service_name] % len(healthy_services)
            self.round_robin_counters[service_name] += 1
            
            return healthy_services[index]
        
        # 默认返回第一个健康的服务
        return healthy_services[0]
    
    async def forward_request(self, request: Request, service_name: str) -> Response:
        """转发请求到后端服务"""
        try:
            # 选择服务实例
            selected_service = self.select_service_instance(service_name)
            if not selected_service:
                raise HTTPException(
                    status_code=503,
                    detail=f"Service {service_name} is not available"
                )
            
            # 获取服务URL
            service_url = get_service_url(selected_service)
            if not service_url:
                raise HTTPException(
                    status_code=503,
                    detail=f"Service {service_name} configuration not found"
                )
            
            # 构建目标URL
            target_url = f"{service_url}{request.url.path}"
            if request.url.query:
                target_url += f"?{request.url.query}"
            
            # 准备请求头
            headers = dict(request.headers)
            
            # 添加用户信息到请求头
            if hasattr(request.state, 'user') and request.state.user:
                headers["X-User-ID"] = request.state.user["user_id"]
                headers["X-Username"] = request.state.user["username"]
                headers["X-User-Roles"] = ",".join(request.state.user.get("roles", []))
            
            # 移除可能导致问题的头部
            headers.pop("host", None)
            headers.pop("content-length", None)
            
            # 读取请求体
            body = await request.body()
            
            # 发送请求
            service_config = settings.BACKEND_SERVICES[selected_service]
            timeout = service_config.get("timeout", 30)
            
            async with httpx.AsyncClient(timeout=timeout) as client:
                response = await client.request(
                    method=request.method,
                    url=target_url,
                    headers=headers,
                    content=body
                )
            
            # 构建响应
            response_headers = dict(response.headers)
            response_headers.pop("content-encoding", None)
            response_headers.pop("content-length", None)
            
            return Response(
                content=response.content,
                status_code=response.status_code,
                headers=response_headers,
                media_type=response.headers.get("content-type")
            )
            
        except httpx.TimeoutException:
            logger.error(f"Request timeout for service {service_name}")
            raise HTTPException(status_code=504, detail="Gateway timeout")
        except httpx.ConnectError:
            logger.error(f"Connection error for service {service_name}")
            raise HTTPException(status_code=503, detail="Service unavailable")
        except Exception as e:
            logger.error(f"Request forwarding error: {e}")
            raise HTTPException(status_code=500, detail="Internal gateway error")
    
    async def dispatch(self, request: Request, call_next):
        """处理请求"""
        
        # 检查是否需要转发到后端服务
        service_name = get_service_for_path(request.url.path)
        
        if service_name:
            # 转发到后端服务
            return await self.forward_request(request, service_name)
        else:
            # 本地处理
            return await call_next(request)
