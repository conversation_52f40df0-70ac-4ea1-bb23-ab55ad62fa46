# AI Service Engine - AI服务引擎

AI服务引擎是MCP智能体系统的核心AI能力提供者，集成了大语言模型、向量数据库、机器学习等AI核心功能。

## 🎯 核心功能

### 大语言模型服务
- **对话生成** - 支持同步和流式对话生成
- **文本分析** - 实体提取、情感分析、文本分类
- **内容生成** - 文本摘要、内容创作
- **知识问答** - 基于知识库的智能问答

### 向量数据库服务
- **文档存储** - 支持文档向量化存储
- **语义搜索** - 基于向量相似度的智能搜索
- **知识管理** - 企业知识库构建和管理
- **相似度计算** - 文本相似度分析

### 机器学习服务
- **嵌入生成** - 文本向量化
- **模型推理** - 支持多种ML模型
- **特征提取** - 文本特征分析
- **预测分析** - 基于历史数据的预测

## 🏗️ 技术架构

```
AI服务引擎架构
├── API层 (FastAPI)
│   ├── 聊天对话API
│   ├── 知识库API
│   ├── 文本分析API
│   └── 向量嵌入API
├── 核心服务层
│   ├── LLM管理器 - 大语言模型管理
│   ├── 向量数据库管理器 - 向量存储管理
│   └── 中间件 - 请求处理和错误处理
├── AI模型层
│   ├── OpenAI GPT-4 - 大语言模型
│   ├── Sentence Transformers - 嵌入模型
│   └── 自定义模型 - 业务特定模型
└── 存储层
    ├── Qdrant - 向量数据库
    ├── Redis - 缓存
    └── 本地存储 - 模型文件
```

## 🚀 快速开始

### 环境要求
- Python 3.11+
- Docker & Docker Compose
- OpenAI API Key

### 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
OPENAI_API_KEY=your_openai_api_key
VECTOR_DB_URL=http://qdrant:6333
```

### 启动服务
```bash
# 使用Docker启动
docker-compose up ai-service

# 或本地开发启动
pip install -r requirements.txt
python main.py
```

### API文档
启动后访问：
- Swagger UI: http://localhost:8009/docs
- ReDoc: http://localhost:8009/redoc

## 📚 API使用示例

### 聊天对话
```python
import requests

# 发送聊天请求
response = requests.post("http://localhost:8009/api/v1/chat/", json={
    "message": "你好，请介绍一下MCP系统",
    "use_knowledge_base": True
})

print(response.json()["response"])
```

### 知识库管理
```python
# 添加文档到知识库
response = requests.post("http://localhost:8009/api/v1/knowledge/documents", json=[{
    "content": "MCP是一个企业经营全流程管理系统...",
    "metadata": {"type": "system_doc"},
    "source": "manual"
}])

# 搜索知识库
response = requests.post("http://localhost:8009/api/v1/knowledge/search", json={
    "query": "什么是MCP系统",
    "limit": 5
})
```

### 文本分析
```python
# 实体提取
response = requests.post("http://localhost:8009/api/v1/analysis/entities", json={
    "text": "张三在北京的ABC公司工作，负责销售业务"
})

# 情感分析
response = requests.post("http://localhost:8009/api/v1/analysis/sentiment", json={
    "text": "这个产品非常好用，我很满意"
})
```

## 🔧 配置说明

### 主要配置项
- `OPENAI_API_KEY`: OpenAI API密钥
- `VECTOR_DB_URL`: 向量数据库连接地址
- `EMBEDDING_MODEL`: 嵌入模型名称
- `CHUNK_SIZE`: 文本分块大小
- `CACHE_TTL`: 缓存过期时间

### 模型配置
- 支持OpenAI GPT-4/GPT-3.5
- 支持本地部署的开源模型
- 可配置多种嵌入模型

## 🔍 监控和日志

### 健康检查
```bash
curl http://localhost:8009/health
```

### 日志查看
```bash
# 查看容器日志
docker logs ai-service

# 实时日志
docker logs -f ai-service
```

## 🛠️ 开发指南

### 添加新的AI功能
1. 在 `app/api/v1/` 下创建新的路由文件
2. 在 `app/core/` 下添加核心逻辑
3. 更新 `app/api/v1/__init__.py` 注册路由

### 集成新的模型
1. 在 `app/core/llm.py` 中添加模型管理
2. 更新配置文件支持新模型
3. 添加相应的API接口

## 🔐 安全考虑

- API密钥安全存储
- 请求频率限制
- 输入内容过滤
- 敏感信息脱敏

## 📈 性能优化

- 向量缓存机制
- 批量处理支持
- 异步请求处理
- 模型预加载

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 📄 许可证

MIT License
