"""
智能体工厂
"""

from typing import Dict, Type, Optional
from loguru import logger

from .base_agent import BaseAgent
from .lead_agent import LeadInsightAgent


class AgentFactory:
    """智能体工厂"""
    
    def __init__(self):
        self._agent_classes: Dict[str, Type[BaseAgent]] = {}
        self._agent_instances: Dict[str, BaseAgent] = {}
        
        # 注册内置智能体
        self._register_builtin_agents()
    
    def _register_builtin_agents(self):
        """注册内置智能体"""
        self.register_agent("lead-insight", LeadInsightAgent)
    
    def register_agent(self, agent_type: str, agent_class: Type[BaseAgent]):
        """注册智能体类"""
        self._agent_classes[agent_type] = agent_class
        logger.info(f"Registered agent type: {agent_type}")
    
    async def create_agent(self, agent_type: str, **kwargs) -> BaseAgent:
        """创建智能体实例"""
        if agent_type not in self._agent_classes:
            raise ValueError(f"Unknown agent type: {agent_type}")
        
        agent_class = self._agent_classes[agent_type]
        
        # 创建实例
        if agent_type == "lead-insight":
            agent = agent_class()
        else:
            agent = agent_class(**kwargs)
        
        # 缓存实例
        self._agent_instances[agent.agent_id] = agent
        
        logger.info(f"Created agent: {agent.agent_name} ({agent.agent_id})")
        return agent
    
    def get_agent(self, agent_id: str) -> Optional[BaseAgent]:
        """获取智能体实例"""
        return self._agent_instances.get(agent_id)
    
    def get_all_agents(self) -> Dict[str, BaseAgent]:
        """获取所有智能体实例"""
        return self._agent_instances.copy()
    
    def get_agent_types(self) -> list:
        """获取支持的智能体类型"""
        return list(self._agent_classes.keys())
    
    async def start_all_agents(self):
        """启动所有智能体"""
        for agent in self._agent_instances.values():
            if not agent.is_running:
                await agent.start()
    
    async def stop_all_agents(self):
        """停止所有智能体"""
        for agent in self._agent_instances.values():
            if agent.is_running:
                await agent.stop()


# 全局智能体工厂实例
agent_factory = AgentFactory()


def get_agent_factory() -> AgentFactory:
    """获取智能体工厂"""
    return agent_factory
