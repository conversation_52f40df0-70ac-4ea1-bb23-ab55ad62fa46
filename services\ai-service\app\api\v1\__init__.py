"""
AI服务API v1
"""

from fastapi import APIRouter

from .chat import router as chat_router
from .knowledge import router as knowledge_router
from .analysis import router as analysis_router
from .embeddings import router as embeddings_router

# 创建API路由器
api_router = APIRouter()

# 注册子路由
api_router.include_router(chat_router, prefix="/chat", tags=["聊天对话"])
api_router.include_router(knowledge_router, prefix="/knowledge", tags=["知识库"])
api_router.include_router(analysis_router, prefix="/analysis", tags=["文本分析"])
api_router.include_router(embeddings_router, prefix="/embeddings", tags=["向量嵌入"])
