"""
日志中间件
"""

from fastapi import Request, Response
from fastapi.middleware.base import BaseHTTPMiddleware
import time
import uuid
import json
from loguru import logger


class LoggingMiddleware(BaseHTTPMiddleware):
    """日志中间件"""
    
    def __init__(self, app):
        super().__init__(app)
    
    def get_client_ip(self, request: Request) -> str:
        """获取客户端IP"""
        # 检查代理头部
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # 使用直连IP
        return request.client.host if request.client else "unknown"
    
    def should_log_body(self, request: Request) -> bool:
        """判断是否应该记录请求体"""
        # 不记录敏感路径的请求体
        sensitive_paths = [
            "/api/v1/auth/login",
            "/api/v1/auth/register",
            "/api/v1/files/upload"
        ]
        
        for path in sensitive_paths:
            if request.url.path.startswith(path):
                return False
        
        # 不记录大文件上传
        content_length = request.headers.get("content-length")
        if content_length and int(content_length) > 1024 * 1024:  # 1MB
            return False
        
        return True
    
    async def dispatch(self, request: Request, call_next):
        """处理请求"""
        
        # 生成请求ID
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        # 记录请求开始时间
        start_time = time.time()
        
        # 获取客户端信息
        client_ip = self.get_client_ip(request)
        user_agent = request.headers.get("user-agent", "")
        
        # 构建请求日志
        request_log = {
            "request_id": request_id,
            "method": request.method,
            "url": str(request.url),
            "path": request.url.path,
            "query_params": dict(request.query_params),
            "client_ip": client_ip,
            "user_agent": user_agent,
            "headers": dict(request.headers),
            "timestamp": time.time()
        }
        
        # 记录请求体（如果适当）
        if self.should_log_body(request):
            try:
                body = await request.body()
                if body:
                    # 尝试解析JSON
                    try:
                        request_log["body"] = json.loads(body.decode())
                    except:
                        request_log["body"] = body.decode()[:1000]  # 限制长度
                
                # 重新构建请求对象（因为body已被读取）
                async def receive():
                    return {"type": "http.request", "body": body}
                
                request._receive = receive
                
            except Exception as e:
                logger.warning(f"Failed to read request body: {e}")
        
        # 记录用户信息（如果已认证）
        if hasattr(request.state, 'user') and request.state.user:
            request_log["user"] = {
                "user_id": request.state.user.get("user_id"),
                "username": request.state.user.get("username"),
                "auth_type": request.state.user.get("auth_type")
            }
        
        # 记录请求开始
        logger.info(f"Request started", extra={"request_log": request_log})
        
        try:
            # 处理请求
            response = await call_next(request)
            
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 构建响应日志
            response_log = {
                "request_id": request_id,
                "status_code": response.status_code,
                "process_time": round(process_time, 3),
                "response_headers": dict(response.headers),
                "timestamp": time.time()
            }
            
            # 添加响应头
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Process-Time"] = str(process_time)
            
            # 记录响应
            if response.status_code >= 400:
                logger.warning(f"Request failed", extra={"response_log": response_log})
            else:
                logger.info(f"Request completed", extra={"response_log": response_log})
            
            return response
            
        except Exception as e:
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录错误
            error_log = {
                "request_id": request_id,
                "error": str(e),
                "error_type": type(e).__name__,
                "process_time": round(process_time, 3),
                "timestamp": time.time()
            }
            
            logger.error(f"Request error", extra={"error_log": error_log})
            raise
