"""
AI服务配置
"""

from pydantic_settings import BaseSettings
from typing import List, Optional
import os


class Settings(BaseSettings):
    """应用配置"""
    
    # 基础配置
    APP_NAME: str = "AI Service Engine"
    VERSION: str = "1.0.0"
    DEBUG: bool = False
    
    # API配置
    API_V1_STR: str = "/api/v1"
    
    # CORS配置
    CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:3001",
        "http://localhost:8000"
    ]
    
    # OpenAI配置
    OPENAI_API_KEY: Optional[str] = None
    OPENAI_MODEL: str = "gpt-4"
    OPENAI_TEMPERATURE: float = 0.7
    OPENAI_MAX_TOKENS: int = 2000
    
    # 向量数据库配置
    VECTOR_DB_URL: str = "http://localhost:6333"
    VECTOR_DB_COLLECTION: str = "mcp_knowledge"
    VECTOR_DIMENSION: int = 1536
    
    # 模型配置
    MODEL_PATH: str = "/models"
    EMBEDDING_MODEL: str = "sentence-transformers/all-MiniLM-L6-v2"
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379"
    REDIS_PASSWORD: Optional[str] = None
    
    # 缓存配置
    CACHE_TTL: int = 3600  # 1小时
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    
    # 安全配置
    SECRET_KEY: str = "your-secret-key-here"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # 文件处理配置
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_FILE_TYPES: List[str] = [
        "text/plain",
        "application/pdf",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/msword"
    ]
    
    # 模型推理配置
    MAX_BATCH_SIZE: int = 32
    INFERENCE_TIMEOUT: int = 30
    
    # 知识库配置
    KNOWLEDGE_BASE_NAME: str = "mcp_knowledge"
    CHUNK_SIZE: int = 1000
    CHUNK_OVERLAP: int = 200
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# 创建全局配置实例
settings = Settings()

# 验证必要的配置
def validate_settings():
    """验证配置"""
    if not settings.OPENAI_API_KEY:
        raise ValueError("OPENAI_API_KEY is required")
    
    if not settings.VECTOR_DB_URL:
        raise ValueError("VECTOR_DB_URL is required")


# 在导入时验证配置
if os.getenv("SKIP_CONFIG_VALIDATION") != "true":
    try:
        validate_settings()
    except ValueError as e:
        print(f"Configuration warning: {e}")
