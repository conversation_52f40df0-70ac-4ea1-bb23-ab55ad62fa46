"""
知识库管理API
"""

from fastapi import APIRouter, HTTPException, Depends, UploadFile, File
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import uuid
from datetime import datetime
from loguru import logger

from ...core.vector_db import get_vector_db, VectorDBManager
from ...core.llm import get_llm, LLMManager

router = APIRouter()


class DocumentRequest(BaseModel):
    """文档请求"""
    content: str
    metadata: Optional[Dict[str, Any]] = {}
    source: Optional[str] = ""


class DocumentResponse(BaseModel):
    """文档响应"""
    id: str
    content: str
    metadata: Dict[str, Any]
    source: str
    timestamp: str


class SearchRequest(BaseModel):
    """搜索请求"""
    query: str
    limit: int = 10
    score_threshold: float = 0.7


class SearchResponse(BaseModel):
    """搜索响应"""
    results: List[Dict[str, Any]]
    total: int


@router.post("/documents", response_model=List[str])
async def add_documents(
    documents: List[DocumentRequest],
    vector_db: VectorDBManager = Depends(get_vector_db)
):
    """添加文档到知识库"""
    try:
        # 准备文档数据
        doc_data = []
        for doc in documents:
            doc_id = str(uuid.uuid4())
            doc_data.append({
                'id': doc_id,
                'content': doc.content,
                'metadata': doc.metadata,
                'source': doc.source,
                'timestamp': datetime.utcnow().isoformat()
            })
        
        # 添加到向量数据库
        document_ids = await vector_db.add_documents(doc_data)
        
        logger.info(f"Added {len(document_ids)} documents to knowledge base")
        return document_ids
        
    except Exception as e:
        logger.error(f"Failed to add documents: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/upload")
async def upload_file(
    file: UploadFile = File(...),
    source: Optional[str] = None,
    vector_db: VectorDBManager = Depends(get_vector_db),
    llm: LLMManager = Depends(get_llm)
):
    """上传文件到知识库"""
    try:
        # 读取文件内容
        content = await file.read()
        
        # 根据文件类型处理内容
        if file.content_type == "text/plain":
            text_content = content.decode('utf-8')
        elif file.content_type == "application/pdf":
            # 这里应该使用PDF解析库
            text_content = "PDF content extraction not implemented"
        else:
            raise HTTPException(status_code=400, detail="Unsupported file type")
        
        # 分割文本
        documents = llm.split_text(text_content)
        
        # 准备文档数据
        doc_data = []
        for i, doc in enumerate(documents):
            doc_id = f"{file.filename}_{i}"
            doc_data.append({
                'id': doc_id,
                'content': doc.page_content,
                'metadata': {
                    'filename': file.filename,
                    'content_type': file.content_type,
                    'chunk_index': i
                },
                'source': source or file.filename,
                'timestamp': datetime.utcnow().isoformat()
            })
        
        # 添加到向量数据库
        document_ids = await vector_db.add_documents(doc_data)
        
        return {
            "message": f"Successfully uploaded {file.filename}",
            "document_ids": document_ids,
            "chunks_created": len(document_ids)
        }
        
    except Exception as e:
        logger.error(f"Failed to upload file: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/search", response_model=SearchResponse)
async def search_knowledge(
    request: SearchRequest,
    vector_db: VectorDBManager = Depends(get_vector_db)
):
    """搜索知识库"""
    try:
        # 执行搜索
        results = await vector_db.search_similar(
            query=request.query,
            limit=request.limit,
            score_threshold=request.score_threshold
        )
        
        return SearchResponse(
            results=results,
            total=len(results)
        )
        
    except Exception as e:
        logger.error(f"Failed to search knowledge: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/documents")
async def delete_documents(
    document_ids: List[str],
    vector_db: VectorDBManager = Depends(get_vector_db)
):
    """删除文档"""
    try:
        success = await vector_db.delete_documents(document_ids)
        
        if success:
            return {"message": f"Successfully deleted {len(document_ids)} documents"}
        else:
            raise HTTPException(status_code=500, detail="Failed to delete documents")
            
    except Exception as e:
        logger.error(f"Failed to delete documents: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/info")
async def get_knowledge_info(
    vector_db: VectorDBManager = Depends(get_vector_db)
):
    """获取知识库信息"""
    try:
        info = await vector_db.get_collection_info()
        return info
        
    except Exception as e:
        logger.error(f"Failed to get knowledge info: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def knowledge_health():
    """知识库服务健康检查"""
    return {"status": "healthy", "service": "knowledge"}
