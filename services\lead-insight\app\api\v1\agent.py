"""
智能体管理API
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from pydantic import BaseModel
from typing import Dict, Any, List, Optional
from loguru import logger

from ...agent.lead_agent import LeadInsightAgent
from ...agent.base_agent import AgentTask, TaskPriority, TaskStatus
from ...core.auth import require_admin

router = APIRouter()

# 全局智能体实例
lead_agent: Optional[LeadInsightAgent] = None


class TaskRequest(BaseModel):
    """任务请求"""
    task_type: str
    data: Dict[str, Any] = {}
    priority: str = "normal"


class TaskResponse(BaseModel):
    """任务响应"""
    task_id: str
    status: str
    message: str


class AgentStatusResponse(BaseModel):
    """智能体状态响应"""
    agent_id: str
    agent_name: str
    is_running: bool
    task_queue_size: int
    current_task: Optional[Dict[str, Any]]
    context: Dict[str, Any]
    learning_data_size: int


class DecisionRequest(BaseModel):
    """决策请求"""
    situation_type: str
    situation_data: Dict[str, Any]


class DecisionResponse(BaseModel):
    """决策响应"""
    decision: str
    confidence: float
    reasoning: str


async def get_agent() -> LeadInsightAgent:
    """获取智能体实例"""
    global lead_agent
    
    if lead_agent is None:
        lead_agent = LeadInsightAgent()
        await lead_agent.start()
    
    return lead_agent


@router.post("/start")
async def start_agent(
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(require_admin)
):
    """启动智能体"""
    try:
        agent = await get_agent()
        
        if agent.is_running:
            return {"message": "Agent is already running"}
        
        background_tasks.add_task(agent.start)
        
        return {"message": "Agent start initiated"}
        
    except Exception as e:
        logger.error(f"Failed to start agent: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/stop")
async def stop_agent(
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(require_admin)
):
    """停止智能体"""
    try:
        agent = await get_agent()
        
        if not agent.is_running:
            return {"message": "Agent is not running"}
        
        background_tasks.add_task(agent.stop)
        
        return {"message": "Agent stop initiated"}
        
    except Exception as e:
        logger.error(f"Failed to stop agent: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status", response_model=AgentStatusResponse)
async def get_agent_status():
    """获取智能体状态"""
    try:
        agent = await get_agent()
        status = agent.get_status()
        
        return AgentStatusResponse(**status)
        
    except Exception as e:
        logger.error(f"Failed to get agent status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks", response_model=TaskResponse)
async def add_task(
    task_request: TaskRequest,
    current_user: dict = Depends(require_admin)
):
    """添加任务"""
    try:
        agent = await get_agent()
        
        # 转换优先级
        priority_map = {
            "low": TaskPriority.LOW,
            "normal": TaskPriority.NORMAL,
            "high": TaskPriority.HIGH,
            "urgent": TaskPriority.URGENT
        }
        
        priority = priority_map.get(task_request.priority.lower(), TaskPriority.NORMAL)
        
        # 创建任务
        task = AgentTask(
            task_type=task_request.task_type,
            data=task_request.data,
            priority=priority
        )
        
        # 添加任务
        await agent.add_task(task)
        
        return TaskResponse(
            task_id=task.task_id,
            status=task.status.value,
            message=f"Task {task.task_id} added successfully"
        )
        
    except Exception as e:
        logger.error(f"Failed to add task: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks")
async def get_tasks(
    current_user: dict = Depends(require_admin)
):
    """获取任务列表"""
    try:
        agent = await get_agent()
        
        tasks = []
        
        # 当前任务
        if agent.current_task:
            tasks.append({
                "type": "current",
                "task": agent.current_task.to_dict()
            })
        
        # 队列中的任务
        for task in agent.task_queue:
            tasks.append({
                "type": "queued",
                "task": task.to_dict()
            })
        
        return {
            "total": len(tasks),
            "tasks": tasks
        }
        
    except Exception as e:
        logger.error(f"Failed to get tasks: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/decision", response_model=DecisionResponse)
async def make_decision(
    decision_request: DecisionRequest,
    current_user: dict = Depends(require_admin)
):
    """请求智能决策"""
    try:
        agent = await get_agent()
        
        situation = {
            "type": decision_request.situation_type,
            **decision_request.situation_data
        }
        
        decision = await agent.make_decision(situation)
        
        return DecisionResponse(**decision)
        
    except Exception as e:
        logger.error(f"Failed to make decision: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/context")
async def update_context(
    context_data: Dict[str, Any],
    current_user: dict = Depends(require_admin)
):
    """更新智能体上下文"""
    try:
        agent = await get_agent()
        
        # 创建上下文更新任务
        task = AgentTask(
            task_type="context_update",
            data={"context": context_data},
            priority=TaskPriority.HIGH
        )
        
        await agent.add_task(task)
        
        return {"message": "Context update task added"}
        
    except Exception as e:
        logger.error(f"Failed to update context: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/context")
async def get_context():
    """获取智能体上下文"""
    try:
        agent = await get_agent()
        return {"context": agent.context}
        
    except Exception as e:
        logger.error(f"Failed to get context: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/learning")
async def update_learning_config(
    learning_config: Dict[str, Any],
    current_user: dict = Depends(require_admin)
):
    """更新学习配置"""
    try:
        agent = await get_agent()
        
        # 创建学习更新任务
        task = AgentTask(
            task_type="learning_update",
            data={"learning_config": learning_config},
            priority=TaskPriority.NORMAL
        )
        
        await agent.add_task(task)
        
        return {"message": "Learning configuration update task added"}
        
    except Exception as e:
        logger.error(f"Failed to update learning config: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/learning/stats")
async def get_learning_stats():
    """获取学习统计"""
    try:
        agent = await get_agent()
        
        if not agent.learning_data:
            return {"message": "No learning data available"}
        
        # 计算统计信息
        total_tasks = len(agent.learning_data)
        successful_tasks = sum(1 for task in agent.learning_data if task["status"] == "completed")
        failed_tasks = sum(1 for task in agent.learning_data if task["status"] == "failed")
        
        success_rate = successful_tasks / total_tasks if total_tasks > 0 else 0
        
        # 计算平均执行时间
        completed_tasks = [task for task in agent.learning_data if task["status"] == "completed"]
        avg_execution_time = 0
        if completed_tasks:
            avg_execution_time = sum(task["execution_time"] for task in completed_tasks) / len(completed_tasks)
        
        return {
            "total_tasks": total_tasks,
            "successful_tasks": successful_tasks,
            "failed_tasks": failed_tasks,
            "success_rate": success_rate,
            "average_execution_time": avg_execution_time
        }
        
    except Exception as e:
        logger.error(f"Failed to get learning stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/score-lead")
async def score_lead_task(
    lead_id: str,
    current_user: dict = Depends(require_admin)
):
    """创建线索评分任务"""
    try:
        agent = await get_agent()
        
        task = AgentTask(
            task_type="score_lead",
            data={"lead_id": lead_id},
            priority=TaskPriority.HIGH
        )
        
        await agent.add_task(task)
        
        return {
            "task_id": task.task_id,
            "message": f"Lead scoring task created for lead {lead_id}"
        }
        
    except Exception as e:
        logger.error(f"Failed to create score lead task: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/analyze-leads")
async def analyze_leads_task(
    current_user: dict = Depends(require_admin)
):
    """创建线索分析任务"""
    try:
        agent = await get_agent()
        
        task = AgentTask(
            task_type="analyze_leads",
            data={"type": "manual"},
            priority=TaskPriority.NORMAL
        )
        
        await agent.add_task(task)
        
        return {
            "task_id": task.task_id,
            "message": "Lead analysis task created"
        }
        
    except Exception as e:
        logger.error(f"Failed to create analyze leads task: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/auto-assign")
async def auto_assign_task(
    current_user: dict = Depends(require_admin)
):
    """创建自动分配任务"""
    try:
        agent = await get_agent()
        
        task = AgentTask(
            task_type="auto_assign_leads",
            data={"type": "manual"},
            priority=TaskPriority.HIGH
        )
        
        await agent.add_task(task)
        
        return {
            "task_id": task.task_id,
            "message": "Auto assignment task created"
        }
        
    except Exception as e:
        logger.error(f"Failed to create auto assign task: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def agent_health():
    """智能体健康检查"""
    try:
        agent = await get_agent()
        
        # 创建健康检查任务
        task = AgentTask(
            task_type="health_check",
            data={},
            priority=TaskPriority.URGENT
        )
        
        await agent.add_task(task)
        
        return {
            "status": "healthy" if agent.is_running else "stopped",
            "agent_name": agent.agent_name,
            "health_check_task": task.task_id
        }
        
    except Exception as e:
        logger.error(f"Agent health check failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))
