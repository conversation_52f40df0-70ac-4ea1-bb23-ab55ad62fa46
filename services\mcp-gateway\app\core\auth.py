"""
认证管理
"""

from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import HTTPException, status
from loguru import logger

from .config import settings
from .redis import get_redis


class AuthManager:
    """认证管理器"""
    
    def __init__(self):
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        self.redis = get_redis()
        
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        return self.pwd_context.verify(plain_password, hashed_password)
    
    def get_password_hash(self, password: str) -> str:
        """获取密码哈希"""
        return self.pwd_context.hash(password)
    
    def create_access_token(self, data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
        """创建访问令牌"""
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
        return encoded_jwt
    
    def create_refresh_token(self, data: Dict[str, Any]) -> str:
        """创建刷新令牌"""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
        to_encode.update({"exp": expire, "type": "refresh"})
        encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
        return encoded_jwt
    
    async def verify_token(self, token: str) -> Dict[str, Any]:
        """验证令牌"""
        try:
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
            
            # 检查令牌是否在黑名单中
            is_blacklisted = await self.redis.exists(f"blacklist:{token}")
            if is_blacklisted:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Token has been revoked"
                )
            
            return payload
            
        except JWTError as e:
            logger.error(f"JWT verification failed: {e}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials"
            )
    
    async def blacklist_token(self, token: str, exp: int):
        """将令牌加入黑名单"""
        try:
            # 计算剩余过期时间
            current_time = datetime.utcnow().timestamp()
            ttl = max(0, int(exp - current_time))
            
            if ttl > 0:
                await self.redis.set(f"blacklist:{token}", "1", ttl)
                logger.info(f"Token blacklisted for {ttl} seconds")
            
        except Exception as e:
            logger.error(f"Failed to blacklist token: {e}")
    
    async def get_user_permissions(self, user_id: str) -> list:
        """获取用户权限"""
        try:
            # 从缓存获取权限
            cache_key = f"user_permissions:{user_id}"
            permissions = await self.redis.get(cache_key)
            
            if permissions is None:
                # 这里应该从数据库获取权限，暂时返回默认权限
                permissions = ["read", "write"]
                await self.redis.set(cache_key, permissions, ttl=3600)
            
            return permissions
            
        except Exception as e:
            logger.error(f"Failed to get user permissions: {e}")
            return []
    
    async def check_permission(self, user_id: str, required_permission: str) -> bool:
        """检查用户权限"""
        try:
            user_permissions = await self.get_user_permissions(user_id)
            return required_permission in user_permissions or "admin" in user_permissions
            
        except Exception as e:
            logger.error(f"Failed to check permission: {e}")
            return False
    
    async def get_api_key_info(self, api_key: str) -> Optional[Dict[str, Any]]:
        """获取API密钥信息"""
        try:
            cache_key = f"api_key:{api_key}"
            api_key_info = await self.redis.get(cache_key)
            
            if api_key_info is None:
                # 这里应该从数据库获取API密钥信息
                # 暂时返回None表示无效的API密钥
                return None
            
            return api_key_info
            
        except Exception as e:
            logger.error(f"Failed to get API key info: {e}")
            return None
    
    async def validate_api_key(self, api_key: str) -> bool:
        """验证API密钥"""
        api_key_info = await self.get_api_key_info(api_key)
        if not api_key_info:
            return False
        
        # 检查API密钥是否过期
        if "expires_at" in api_key_info:
            expires_at = datetime.fromisoformat(api_key_info["expires_at"])
            if datetime.utcnow() > expires_at:
                return False
        
        # 检查API密钥是否被禁用
        if api_key_info.get("disabled", False):
            return False
        
        return True


# 全局认证管理器实例
auth_manager = AuthManager()


async def init_auth():
    """初始化认证系统"""
    logger.info("Authentication system initialized")


def get_auth() -> AuthManager:
    """获取认证管理器"""
    return auth_manager
