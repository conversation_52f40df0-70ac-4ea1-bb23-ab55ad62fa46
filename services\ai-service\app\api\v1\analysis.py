"""
文本分析API
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from loguru import logger

from ...core.llm import get_llm, LLMManager

router = APIRouter()


class TextAnalysisRequest(BaseModel):
    """文本分析请求"""
    text: str


class EntityExtractionRequest(BaseModel):
    """实体提取请求"""
    text: str


class SummarizationRequest(BaseModel):
    """摘要请求"""
    text: str
    max_length: int = 200


class ClassificationRequest(BaseModel):
    """分类请求"""
    text: str
    categories: List[str]


class SentimentAnalysisRequest(BaseModel):
    """情感分析请求"""
    text: str


class EntityExtractionResponse(BaseModel):
    """实体提取响应"""
    entities: Dict[str, List[str]]


class SummarizationResponse(BaseModel):
    """摘要响应"""
    summary: str
    original_length: int
    summary_length: int


class ClassificationResponse(BaseModel):
    """分类响应"""
    classifications: Dict[str, float]
    predicted_category: str


class SentimentAnalysisResponse(BaseModel):
    """情感分析响应"""
    sentiment: str
    confidence: float
    details: Dict[str, float]


@router.post("/entities", response_model=EntityExtractionResponse)
async def extract_entities(
    request: EntityExtractionRequest,
    llm: LLMManager = Depends(get_llm)
):
    """提取文本实体"""
    try:
        entities = await llm.extract_entities(request.text)
        
        return EntityExtractionResponse(entities=entities)
        
    except Exception as e:
        logger.error(f"Failed to extract entities: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/summarize", response_model=SummarizationResponse)
async def summarize_text(
    request: SummarizationRequest,
    llm: LLMManager = Depends(get_llm)
):
    """文本摘要"""
    try:
        summary = await llm.summarize_text(request.text, request.max_length)
        
        return SummarizationResponse(
            summary=summary,
            original_length=len(request.text),
            summary_length=len(summary)
        )
        
    except Exception as e:
        logger.error(f"Failed to summarize text: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/classify", response_model=ClassificationResponse)
async def classify_text(
    request: ClassificationRequest,
    llm: LLMManager = Depends(get_llm)
):
    """文本分类"""
    try:
        classifications = await llm.classify_text(request.text, request.categories)
        
        # 找出最高置信度的类别
        predicted_category = max(classifications.items(), key=lambda x: x[1])[0]
        
        return ClassificationResponse(
            classifications=classifications,
            predicted_category=predicted_category
        )
        
    except Exception as e:
        logger.error(f"Failed to classify text: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/sentiment", response_model=SentimentAnalysisResponse)
async def analyze_sentiment(
    request: SentimentAnalysisRequest,
    llm: LLMManager = Depends(get_llm)
):
    """情感分析"""
    try:
        # 使用分类功能进行情感分析
        sentiment_categories = ["积极", "消极", "中性"]
        classifications = await llm.classify_text(request.text, sentiment_categories)
        
        # 确定主要情感
        predicted_sentiment = max(classifications.items(), key=lambda x: x[1])[0]
        confidence = classifications[predicted_sentiment]
        
        return SentimentAnalysisResponse(
            sentiment=predicted_sentiment,
            confidence=confidence,
            details=classifications
        )
        
    except Exception as e:
        logger.error(f"Failed to analyze sentiment: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tokens")
async def count_tokens(
    request: TextAnalysisRequest,
    llm: LLMManager = Depends(get_llm)
):
    """计算token数量"""
    try:
        token_count = llm.count_tokens(request.text)
        
        return {
            "text_length": len(request.text),
            "token_count": token_count,
            "estimated_cost": token_count * 0.0001  # 估算成本
        }
        
    except Exception as e:
        logger.error(f"Failed to count tokens: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/split")
async def split_text(
    request: TextAnalysisRequest,
    llm: LLMManager = Depends(get_llm)
):
    """分割文本"""
    try:
        documents = llm.split_text(request.text)
        
        chunks = []
        for i, doc in enumerate(documents):
            chunks.append({
                "index": i,
                "content": doc.page_content,
                "length": len(doc.page_content),
                "tokens": llm.count_tokens(doc.page_content)
            })
        
        return {
            "original_length": len(request.text),
            "chunks_count": len(chunks),
            "chunks": chunks
        }
        
    except Exception as e:
        logger.error(f"Failed to split text: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def analysis_health():
    """文本分析服务健康检查"""
    return {"status": "healthy", "service": "analysis"}
