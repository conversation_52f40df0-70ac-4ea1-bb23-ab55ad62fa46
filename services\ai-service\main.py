"""
AI Service Engine - AI服务引擎
负责大语言模型、向量数据库、机器学习等AI核心功能
"""

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import uvicorn
from loguru import logger

from app.core.config import settings
from app.core.vector_db import init_vector_db
from app.core.llm import init_llm
from app.api.v1 import api_router
from app.core.middleware import setup_middleware


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    logger.info("Starting AI Service Engine...")

    # 初始化向量数据库
    await init_vector_db()
    logger.info("Vector database initialized")

    # 初始化大语言模型
    await init_llm()
    logger.info("Large Language Model initialized")

    yield

    # 关闭时清理
    logger.info("Shutting down AI Service Engine...")


# 创建FastAPI应用
app = FastAPI(
    title="AI Service Engine",
    description="AI服务引擎 - 提供大语言模型、向量数据库、机器学习等AI核心功能",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 设置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 设置中间件
setup_middleware(app)

# 注册路由
app.include_router(api_router, prefix="/api/v1")

# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "ai-service",
        "version": "1.0.0"
    }

# 根路径
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "AI Service Engine",
        "docs": "/docs",
        "health": "/health"
    }


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8009,
        reload=True,
        log_level="info"
    )
