"""
服务发现
"""

from typing import Dict, List, Optional
import asyncio
from loguru import logger

from .config import settings


class ServiceDiscovery:
    """服务发现管理器"""
    
    def __init__(self):
        self.services: Dict[str, List[str]] = {}
        self.discovery_type = settings.SERVICE_DISCOVERY_TYPE
        
    async def initialize(self):
        """初始化服务发现"""
        if self.discovery_type == "static":
            await self._init_static_discovery()
        elif self.discovery_type == "consul":
            await self._init_consul_discovery()
        elif self.discovery_type == "etcd":
            await self._init_etcd_discovery()
        else:
            logger.warning(f"Unknown service discovery type: {self.discovery_type}")
            await self._init_static_discovery()
    
    async def _init_static_discovery(self):
        """初始化静态服务发现"""
        logger.info("Using static service discovery")
        
        # 从配置文件加载服务列表
        for service_name, service_config in settings.BACKEND_SERVICES.items():
            self.services[service_name] = [service_config["url"]]
        
        logger.info(f"Loaded {len(self.services)} services from static configuration")
    
    async def _init_consul_discovery(self):
        """初始化Consul服务发现"""
        try:
            import consul
            
            self.consul_client = consul.Consul(
                host=settings.CONSUL_HOST,
                port=settings.CONSUL_PORT
            )
            
            logger.info("Consul service discovery initialized")
            
            # 启动服务监控任务
            asyncio.create_task(self._consul_watch_services())
            
        except ImportError:
            logger.error("Consul library not installed, falling back to static discovery")
            await self._init_static_discovery()
        except Exception as e:
            logger.error(f"Failed to initialize Consul: {e}")
            await self._init_static_discovery()
    
    async def _init_etcd_discovery(self):
        """初始化etcd服务发现"""
        try:
            import etcd3
            
            self.etcd_client = etcd3.client()
            
            logger.info("etcd service discovery initialized")
            
            # 启动服务监控任务
            asyncio.create_task(self._etcd_watch_services())
            
        except ImportError:
            logger.error("etcd3 library not installed, falling back to static discovery")
            await self._init_static_discovery()
        except Exception as e:
            logger.error(f"Failed to initialize etcd: {e}")
            await self._init_static_discovery()
    
    async def _consul_watch_services(self):
        """监控Consul服务变化"""
        while True:
            try:
                # 获取所有健康的服务
                services = self.consul_client.health.service('mcp-service', passing=True)[1]
                
                # 更新服务列表
                service_map = {}
                for service in services:
                    service_name = service['Service']['Service']
                    service_address = service['Service']['Address']
                    service_port = service['Service']['Port']
                    service_url = f"http://{service_address}:{service_port}"
                    
                    if service_name not in service_map:
                        service_map[service_name] = []
                    service_map[service_name].append(service_url)
                
                self.services = service_map
                logger.debug(f"Updated services from Consul: {len(self.services)} services")
                
                await asyncio.sleep(30)  # 每30秒检查一次
                
            except Exception as e:
                logger.error(f"Consul service watch error: {e}")
                await asyncio.sleep(60)
    
    async def _etcd_watch_services(self):
        """监控etcd服务变化"""
        while True:
            try:
                # 获取所有服务
                services_data = self.etcd_client.get_prefix('/services/')
                
                service_map = {}
                for value, metadata in services_data:
                    if value:
                        import json
                        service_info = json.loads(value.decode())
                        service_name = service_info['name']
                        service_url = service_info['url']
                        
                        if service_name not in service_map:
                            service_map[service_name] = []
                        service_map[service_name].append(service_url)
                
                self.services = service_map
                logger.debug(f"Updated services from etcd: {len(self.services)} services")
                
                await asyncio.sleep(30)
                
            except Exception as e:
                logger.error(f"etcd service watch error: {e}")
                await asyncio.sleep(60)
    
    def get_service_instances(self, service_name: str) -> List[str]:
        """获取服务实例列表"""
        return self.services.get(service_name, [])
    
    def get_all_services(self) -> Dict[str, List[str]]:
        """获取所有服务"""
        return self.services.copy()
    
    async def register_service(self, service_name: str, service_url: str):
        """注册服务"""
        if self.discovery_type == "consul":
            await self._consul_register_service(service_name, service_url)
        elif self.discovery_type == "etcd":
            await self._etcd_register_service(service_name, service_url)
        else:
            # 静态模式下手动添加
            if service_name not in self.services:
                self.services[service_name] = []
            if service_url not in self.services[service_name]:
                self.services[service_name].append(service_url)
    
    async def _consul_register_service(self, service_name: str, service_url: str):
        """在Consul中注册服务"""
        try:
            from urllib.parse import urlparse
            parsed_url = urlparse(service_url)
            
            self.consul_client.agent.service.register(
                name=service_name,
                service_id=f"{service_name}-{parsed_url.hostname}-{parsed_url.port}",
                address=parsed_url.hostname,
                port=parsed_url.port,
                check=consul.Check.http(f"{service_url}/health", interval="10s")
            )
            
            logger.info(f"Registered service {service_name} in Consul")
            
        except Exception as e:
            logger.error(f"Failed to register service in Consul: {e}")
    
    async def _etcd_register_service(self, service_name: str, service_url: str):
        """在etcd中注册服务"""
        try:
            import json
            
            service_info = {
                "name": service_name,
                "url": service_url,
                "timestamp": time.time()
            }
            
            key = f"/services/{service_name}/{service_url}"
            value = json.dumps(service_info)
            
            self.etcd_client.put(key, value, lease=self.etcd_client.lease(ttl=60))
            
            logger.info(f"Registered service {service_name} in etcd")
            
        except Exception as e:
            logger.error(f"Failed to register service in etcd: {e}")


# 全局服务发现实例
service_discovery = ServiceDiscovery()


async def init_service_discovery():
    """初始化服务发现"""
    await service_discovery.initialize()


def get_service_discovery() -> ServiceDiscovery:
    """获取服务发现管理器"""
    return service_discovery
