"""
智能体基类
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Callable
import asyncio
import uuid
from datetime import datetime
from enum import Enum
from loguru import logger

from ..core.config import settings


class TaskStatus(Enum):
    """任务状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskPriority(Enum):
    """任务优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


class AgentTask:
    """智能体任务"""
    
    def __init__(self, task_id: str = None, task_type: str = "", 
                 data: Dict[str, Any] = None, priority: TaskPriority = TaskPriority.NORMAL):
        self.task_id = task_id or str(uuid.uuid4())
        self.task_type = task_type
        self.data = data or {}
        self.priority = priority
        self.status = TaskStatus.PENDING
        self.created_at = datetime.utcnow()
        self.started_at: Optional[datetime] = None
        self.completed_at: Optional[datetime] = None
        self.result: Optional[Dict[str, Any]] = None
        self.error: Optional[str] = None
        self.retry_count = 0
        self.max_retries = 3
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "task_id": self.task_id,
            "task_type": self.task_type,
            "data": self.data,
            "priority": self.priority.value,
            "status": self.status.value,
            "created_at": self.created_at.isoformat(),
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "result": self.result,
            "error": self.error,
            "retry_count": self.retry_count,
            "max_retries": self.max_retries
        }


class BaseAgent(ABC):
    """智能体基类"""
    
    def __init__(self, agent_id: str, agent_name: str):
        self.agent_id = agent_id
        self.agent_name = agent_name
        self.is_running = False
        self.task_queue: List[AgentTask] = []
        self.current_task: Optional[AgentTask] = None
        self.task_handlers: Dict[str, Callable] = {}
        self.context: Dict[str, Any] = {}
        self.learning_data: List[Dict[str, Any]] = []
        
        # 注册基础任务处理器
        self._register_base_handlers()
    
    def _register_base_handlers(self):
        """注册基础任务处理器"""
        self.task_handlers.update({
            "health_check": self._handle_health_check,
            "context_update": self._handle_context_update,
            "learning_update": self._handle_learning_update
        })
    
    @abstractmethod
    async def initialize(self):
        """初始化智能体"""
        pass
    
    @abstractmethod
    async def process_task(self, task: AgentTask) -> Dict[str, Any]:
        """处理任务（子类实现）"""
        pass
    
    async def start(self):
        """启动智能体"""
        if self.is_running:
            logger.warning(f"Agent {self.agent_name} is already running")
            return
        
        logger.info(f"Starting agent {self.agent_name}")
        self.is_running = True
        
        # 初始化智能体
        await self.initialize()
        
        # 启动任务处理循环
        asyncio.create_task(self._task_processing_loop())
        
        logger.info(f"Agent {self.agent_name} started successfully")
    
    async def stop(self):
        """停止智能体"""
        logger.info(f"Stopping agent {self.agent_name}")
        self.is_running = False
        
        # 等待当前任务完成
        if self.current_task:
            logger.info(f"Waiting for current task {self.current_task.task_id} to complete")
            # 这里可以添加超时机制
    
    async def add_task(self, task: AgentTask):
        """添加任务到队列"""
        logger.info(f"Adding task {task.task_id} of type {task.task_type} to agent {self.agent_name}")
        
        # 按优先级插入任务
        inserted = False
        for i, existing_task in enumerate(self.task_queue):
            if task.priority.value > existing_task.priority.value:
                self.task_queue.insert(i, task)
                inserted = True
                break
        
        if not inserted:
            self.task_queue.append(task)
        
        logger.debug(f"Task queue size: {len(self.task_queue)}")
    
    async def _task_processing_loop(self):
        """任务处理循环"""
        while self.is_running:
            try:
                if self.task_queue and not self.current_task:
                    # 获取下一个任务
                    task = self.task_queue.pop(0)
                    await self._execute_task(task)
                
                # 短暂休眠避免CPU占用过高
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Error in task processing loop: {e}")
                await asyncio.sleep(1)
    
    async def _execute_task(self, task: AgentTask):
        """执行任务"""
        self.current_task = task
        task.status = TaskStatus.RUNNING
        task.started_at = datetime.utcnow()
        
        logger.info(f"Executing task {task.task_id} of type {task.task_type}")
        
        try:
            # 检查是否有专门的处理器
            if task.task_type in self.task_handlers:
                result = await self.task_handlers[task.task_type](task)
            else:
                # 使用通用处理器
                result = await self.process_task(task)
            
            # 任务成功完成
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.utcnow()
            task.result = result
            
            logger.info(f"Task {task.task_id} completed successfully")
            
            # 学习和优化
            await self._learn_from_task(task)
            
        except Exception as e:
            logger.error(f"Task {task.task_id} failed: {e}")
            
            task.error = str(e)
            task.retry_count += 1
            
            # 重试逻辑
            if task.retry_count <= task.max_retries:
                logger.info(f"Retrying task {task.task_id} (attempt {task.retry_count})")
                task.status = TaskStatus.PENDING
                await self.add_task(task)
            else:
                task.status = TaskStatus.FAILED
                task.completed_at = datetime.utcnow()
                logger.error(f"Task {task.task_id} failed after {task.max_retries} retries")
        
        finally:
            self.current_task = None
    
    async def _learn_from_task(self, task: AgentTask):
        """从任务执行中学习"""
        try:
            # 记录学习数据
            learning_record = {
                "task_id": task.task_id,
                "task_type": task.task_type,
                "status": task.status.value,
                "execution_time": (task.completed_at - task.started_at).total_seconds() if task.completed_at and task.started_at else 0,
                "retry_count": task.retry_count,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            self.learning_data.append(learning_record)
            
            # 保持学习数据在合理范围内
            if len(self.learning_data) > 1000:
                self.learning_data = self.learning_data[-500:]
            
            # 分析和优化
            await self._analyze_performance()
            
        except Exception as e:
            logger.error(f"Error in learning process: {e}")
    
    async def _analyze_performance(self):
        """分析性能并优化"""
        try:
            if len(self.learning_data) < 10:
                return
            
            # 计算成功率
            recent_tasks = self.learning_data[-50:]
            success_rate = sum(1 for task in recent_tasks if task["status"] == "completed") / len(recent_tasks)
            
            # 计算平均执行时间
            completed_tasks = [task for task in recent_tasks if task["status"] == "completed"]
            if completed_tasks:
                avg_execution_time = sum(task["execution_time"] for task in completed_tasks) / len(completed_tasks)
                
                # 更新上下文
                self.context.update({
                    "success_rate": success_rate,
                    "avg_execution_time": avg_execution_time,
                    "last_analysis": datetime.utcnow().isoformat()
                })
                
                logger.debug(f"Agent {self.agent_name} performance: success_rate={success_rate:.2f}, avg_time={avg_execution_time:.2f}s")
            
        except Exception as e:
            logger.error(f"Error in performance analysis: {e}")
    
    async def _handle_health_check(self, task: AgentTask) -> Dict[str, Any]:
        """处理健康检查任务"""
        return {
            "agent_id": self.agent_id,
            "agent_name": self.agent_name,
            "status": "healthy",
            "is_running": self.is_running,
            "task_queue_size": len(self.task_queue),
            "current_task": self.current_task.task_id if self.current_task else None,
            "context": self.context,
            "timestamp": datetime.utcnow().isoformat()
        }
    
    async def _handle_context_update(self, task: AgentTask) -> Dict[str, Any]:
        """处理上下文更新任务"""
        new_context = task.data.get("context", {})
        self.context.update(new_context)
        
        return {
            "message": "Context updated successfully",
            "updated_keys": list(new_context.keys())
        }
    
    async def _handle_learning_update(self, task: AgentTask) -> Dict[str, Any]:
        """处理学习更新任务"""
        learning_config = task.data.get("learning_config", {})
        
        # 这里可以更新学习参数
        # 例如调整重试次数、超时时间等
        
        return {
            "message": "Learning configuration updated",
            "config": learning_config
        }
    
    def get_status(self) -> Dict[str, Any]:
        """获取智能体状态"""
        return {
            "agent_id": self.agent_id,
            "agent_name": self.agent_name,
            "is_running": self.is_running,
            "task_queue_size": len(self.task_queue),
            "current_task": self.current_task.to_dict() if self.current_task else None,
            "context": self.context,
            "learning_data_size": len(self.learning_data)
        }
    
    def register_task_handler(self, task_type: str, handler: Callable):
        """注册任务处理器"""
        self.task_handlers[task_type] = handler
        logger.info(f"Registered handler for task type: {task_type}")
    
    async def make_decision(self, situation: Dict[str, Any]) -> Dict[str, Any]:
        """智能决策（子类可重写）"""
        # 基础决策逻辑
        return {
            "decision": "default",
            "confidence": 0.5,
            "reasoning": "Default decision from base agent"
        }
