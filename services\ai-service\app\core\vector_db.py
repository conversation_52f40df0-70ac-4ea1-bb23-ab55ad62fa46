"""
向量数据库管理
"""

from typing import List, Dict, Any, Optional
import asyncio
from qdrant_client import QdrantClient
from qdrant_client.http import models
from qdrant_client.http.models import Distance, VectorParams, PointStruct
from sentence_transformers import SentenceTransformer
import numpy as np
from loguru import logger

from .config import settings


class VectorDBManager:
    """向量数据库管理器"""
    
    def __init__(self):
        self.client: Optional[QdrantClient] = None
        self.embedding_model: Optional[SentenceTransformer] = None
        self.collection_name = settings.VECTOR_DB_COLLECTION
        
    async def initialize(self):
        """初始化向量数据库连接"""
        try:
            # 初始化Qdrant客户端
            self.client = QdrantClient(url=settings.VECTOR_DB_URL)
            
            # 初始化嵌入模型
            self.embedding_model = SentenceTransformer(settings.EMBEDDING_MODEL)
            
            # 创建集合（如果不存在）
            await self._create_collection_if_not_exists()
            
            logger.info("Vector database initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize vector database: {e}")
            raise
    
    async def _create_collection_if_not_exists(self):
        """创建集合（如果不存在）"""
        try:
            # 检查集合是否存在
            collections = self.client.get_collections()
            collection_names = [col.name for col in collections.collections]
            
            if self.collection_name not in collection_names:
                # 创建集合
                self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=VectorParams(
                        size=settings.VECTOR_DIMENSION,
                        distance=Distance.COSINE
                    )
                )
                logger.info(f"Created collection: {self.collection_name}")
            else:
                logger.info(f"Collection already exists: {self.collection_name}")
                
        except Exception as e:
            logger.error(f"Failed to create collection: {e}")
            raise
    
    async def add_documents(self, documents: List[Dict[str, Any]]) -> List[str]:
        """添加文档到向量数据库"""
        try:
            points = []
            document_ids = []
            
            for i, doc in enumerate(documents):
                # 生成文档ID
                doc_id = doc.get('id', f"doc_{i}")
                document_ids.append(doc_id)
                
                # 获取文档文本
                text = doc.get('content', '')
                if not text:
                    continue
                
                # 生成嵌入向量
                embedding = self.embedding_model.encode(text).tolist()
                
                # 创建点
                point = PointStruct(
                    id=doc_id,
                    vector=embedding,
                    payload={
                        'content': text,
                        'metadata': doc.get('metadata', {}),
                        'source': doc.get('source', ''),
                        'timestamp': doc.get('timestamp', '')
                    }
                )
                points.append(point)
            
            # 批量插入
            if points:
                self.client.upsert(
                    collection_name=self.collection_name,
                    points=points
                )
                logger.info(f"Added {len(points)} documents to vector database")
            
            return document_ids
            
        except Exception as e:
            logger.error(f"Failed to add documents: {e}")
            raise
    
    async def search_similar(self, query: str, limit: int = 10, 
                           score_threshold: float = 0.7) -> List[Dict[str, Any]]:
        """搜索相似文档"""
        try:
            # 生成查询向量
            query_vector = self.embedding_model.encode(query).tolist()
            
            # 执行搜索
            search_result = self.client.search(
                collection_name=self.collection_name,
                query_vector=query_vector,
                limit=limit,
                score_threshold=score_threshold
            )
            
            # 格式化结果
            results = []
            for hit in search_result:
                results.append({
                    'id': hit.id,
                    'score': hit.score,
                    'content': hit.payload.get('content', ''),
                    'metadata': hit.payload.get('metadata', {}),
                    'source': hit.payload.get('source', ''),
                    'timestamp': hit.payload.get('timestamp', '')
                })
            
            logger.info(f"Found {len(results)} similar documents")
            return results
            
        except Exception as e:
            logger.error(f"Failed to search similar documents: {e}")
            raise
    
    async def delete_documents(self, document_ids: List[str]) -> bool:
        """删除文档"""
        try:
            self.client.delete(
                collection_name=self.collection_name,
                points_selector=models.PointIdsList(
                    points=document_ids
                )
            )
            logger.info(f"Deleted {len(document_ids)} documents")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete documents: {e}")
            return False
    
    async def get_collection_info(self) -> Dict[str, Any]:
        """获取集合信息"""
        try:
            info = self.client.get_collection(self.collection_name)
            return {
                'name': info.config.params.vectors.size,
                'vectors_count': info.vectors_count,
                'points_count': info.points_count,
                'status': info.status
            }
        except Exception as e:
            logger.error(f"Failed to get collection info: {e}")
            return {}


# 全局向量数据库管理器实例
vector_db_manager = VectorDBManager()


async def init_vector_db():
    """初始化向量数据库"""
    await vector_db_manager.initialize()


def get_vector_db() -> VectorDBManager:
    """获取向量数据库管理器"""
    return vector_db_manager
