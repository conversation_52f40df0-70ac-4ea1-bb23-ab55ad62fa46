"""
认证中间件
"""

from fastapi import Request, Response, HTTPException, status
from fastapi.middleware.base import BaseHTTPMiddleware
from fastapi.responses import JSONResponse
import re
from loguru import logger

from ..core.auth import get_auth
from ..core.config import settings


class AuthMiddleware(BaseHTTPMiddleware):
    """认证中间件"""
    
    def __init__(self, app):
        super().__init__(app)
        self.auth = get_auth()
        
        # 不需要认证的路径
        self.public_paths = [
            r"^/docs.*",
            r"^/redoc.*",
            r"^/openapi\.json$",
            r"^/health$",
            r"^/$",
            r"^/api/v1/auth/login$",
            r"^/api/v1/auth/register$",
            r"^/api/v1/auth/refresh$",
            r"^/metrics$"
        ]
    
    def is_public_path(self, path: str) -> bool:
        """检查是否为公开路径"""
        for pattern in self.public_paths:
            if re.match(pattern, path):
                return True
        return False
    
    async def dispatch(self, request: Request, call_next):
        """处理请求"""
        
        # 检查是否为公开路径
        if self.is_public_path(request.url.path):
            return await call_next(request)
        
        try:
            # 获取认证信息
            authorization = request.headers.get("Authorization")
            api_key = request.headers.get("X-API-Key")
            
            user_info = None
            
            if authorization:
                # JWT令牌认证
                if not authorization.startswith("Bearer "):
                    return JSONResponse(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        content={"error": "Invalid authorization header format"}
                    )
                
                token = authorization.split(" ")[1]
                payload = await self.auth.verify_token(token)
                
                user_info = {
                    "user_id": payload.get("sub"),
                    "username": payload.get("username"),
                    "roles": payload.get("roles", []),
                    "permissions": payload.get("permissions", []),
                    "auth_type": "jwt"
                }
                
            elif api_key:
                # API密钥认证
                is_valid = await self.auth.validate_api_key(api_key)
                if not is_valid:
                    return JSONResponse(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        content={"error": "Invalid API key"}
                    )
                
                api_key_info = await self.auth.get_api_key_info(api_key)
                user_info = {
                    "user_id": api_key_info.get("user_id"),
                    "username": api_key_info.get("username"),
                    "roles": api_key_info.get("roles", []),
                    "permissions": api_key_info.get("permissions", []),
                    "auth_type": "api_key"
                }
                
            else:
                return JSONResponse(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    content={"error": "Authentication required"}
                )
            
            # 将用户信息添加到请求状态
            request.state.user = user_info
            
            # 继续处理请求
            response = await call_next(request)
            
            return response
            
        except HTTPException as e:
            return JSONResponse(
                status_code=e.status_code,
                content={"error": e.detail}
            )
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={"error": "Authentication service error"}
            )
