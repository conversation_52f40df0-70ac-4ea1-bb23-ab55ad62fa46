"""
Redis连接管理
"""

import redis.asyncio as redis
from typing import Optional, Any
import json
import time
from loguru import logger

from .config import settings


class RedisManager:
    """Redis管理器"""
    
    def __init__(self):
        self.redis_client: Optional[redis.Redis] = None
        
    async def initialize(self):
        """初始化Redis连接"""
        try:
            self.redis_client = redis.from_url(
                settings.REDIS_URL,
                password=settings.REDIS_PASSWORD,
                db=settings.REDIS_DB,
                decode_responses=True
            )
            
            # 测试连接
            await self.redis_client.ping()
            logger.info("Redis connection established")
            
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        try:
            if not self.redis_client:
                return None
                
            value = await self.redis_client.get(key)
            if value:
                return json.loads(value)
            return None
            
        except Exception as e:
            logger.error(f"Failed to get cache key {key}: {e}")
            return None
    
    async def set(self, key: str, value: Any, ttl: int = None) -> bool:
        """设置缓存值"""
        try:
            if not self.redis_client:
                return False
                
            json_value = json.dumps(value, ensure_ascii=False)
            
            if ttl:
                await self.redis_client.setex(key, ttl, json_value)
            else:
                await self.redis_client.set(key, json_value)
                
            return True
            
        except Exception as e:
            logger.error(f"Failed to set cache key {key}: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存值"""
        try:
            if not self.redis_client:
                return False
                
            result = await self.redis_client.delete(key)
            return result > 0
            
        except Exception as e:
            logger.error(f"Failed to delete cache key {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        try:
            if not self.redis_client:
                return False
                
            result = await self.redis_client.exists(key)
            return result > 0
            
        except Exception as e:
            logger.error(f"Failed to check cache key {key}: {e}")
            return False
    
    async def incr(self, key: str, amount: int = 1) -> int:
        """递增计数器"""
        try:
            if not self.redis_client:
                return 0
                
            result = await self.redis_client.incrby(key, amount)
            return result
            
        except Exception as e:
            logger.error(f"Failed to increment key {key}: {e}")
            return 0
    
    async def expire(self, key: str, ttl: int) -> bool:
        """设置过期时间"""
        try:
            if not self.redis_client:
                return False
                
            result = await self.redis_client.expire(key, ttl)
            return result
            
        except Exception as e:
            logger.error(f"Failed to set expiry for key {key}: {e}")
            return False
    
    async def get_rate_limit_info(self, key: str, window: int, limit: int) -> dict:
        """获取限流信息"""
        try:
            if not self.redis_client:
                return {"allowed": True, "remaining": limit, "reset_time": 0}
            
            # 使用滑动窗口限流算法
            current_time = int(time.time())
            window_start = current_time - window
            
            # 清理过期的请求记录
            await self.redis_client.zremrangebyscore(key, 0, window_start)
            
            # 获取当前窗口内的请求数
            current_requests = await self.redis_client.zcard(key)
            
            if current_requests < limit:
                # 添加当前请求
                await self.redis_client.zadd(key, {str(current_time): current_time})
                await self.redis_client.expire(key, window)
                
                return {
                    "allowed": True,
                    "remaining": limit - current_requests - 1,
                    "reset_time": current_time + window
                }
            else:
                return {
                    "allowed": False,
                    "remaining": 0,
                    "reset_time": current_time + window
                }
                
        except Exception as e:
            logger.error(f"Failed to check rate limit for key {key}: {e}")
            return {"allowed": True, "remaining": limit, "reset_time": 0}


# 全局Redis管理器实例
redis_manager = RedisManager()


async def init_redis():
    """初始化Redis"""
    await redis_manager.initialize()


def get_redis() -> RedisManager:
    """获取Redis管理器"""
    return redis_manager
