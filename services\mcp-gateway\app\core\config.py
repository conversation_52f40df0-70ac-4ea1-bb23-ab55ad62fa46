"""
网关服务配置
"""

from pydantic_settings import BaseSettings
from typing import List, Dict, Any, Optional
import os


class Settings(BaseSettings):
    """应用配置"""
    
    # 基础配置
    APP_NAME: str = "MCP Gateway Service"
    VERSION: str = "1.0.0"
    DEBUG: bool = False
    
    # API配置
    API_V1_STR: str = "/api/v1"
    
    # CORS配置
    CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:3001",
        "http://localhost:8000"
    ]
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379"
    REDIS_PASSWORD: Optional[str] = None
    REDIS_DB: int = 0
    
    # JWT配置
    SECRET_KEY: str = "your-secret-key-here"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # 限流配置
    RATE_LIMIT_REQUESTS: int = 100
    RATE_LIMIT_WINDOW: int = 60  # 秒
    RATE_LIMIT_ENABLED: bool = True
    
    # 服务发现配置
    SERVICE_DISCOVERY_TYPE: str = "static"  # static, consul, etcd
    CONSUL_HOST: str = "localhost"
    CONSUL_PORT: int = 8500
    
    # 后端服务配置
    BACKEND_SERVICES: Dict[str, Dict[str, Any]] = {
        "mcp-core": {
            "url": "http://mcp-core:8001",
            "health_check": "/health",
            "timeout": 30,
            "retry": 3
        },
        "lead-insight": {
            "url": "http://lead-insight:8002",
            "health_check": "/health",
            "timeout": 30,
            "retry": 3
        },
        "sales-contract": {
            "url": "http://sales-contract:8003",
            "health_check": "/health",
            "timeout": 30,
            "retry": 3
        },
        "project-execution": {
            "url": "http://project-execution:8004",
            "health_check": "/health",
            "timeout": 30,
            "retry": 3
        },
        "bom-supply": {
            "url": "http://bom-supply:8005",
            "health_check": "/health",
            "timeout": 30,
            "retry": 3
        },
        "after-sales": {
            "url": "http://after-sales:8006",
            "health_check": "/health",
            "timeout": 30,
            "retry": 3
        },
        "finance-settlement": {
            "url": "http://finance-settlement:8007",
            "health_check": "/health",
            "timeout": 30,
            "retry": 3
        },
        "data-analytics": {
            "url": "http://data-analytics:8008",
            "health_check": "/health",
            "timeout": 30,
            "retry": 3
        },
        "ai-service": {
            "url": "http://ai-service:8009",
            "health_check": "/health",
            "timeout": 60,
            "retry": 3
        },
        "workflow-engine": {
            "url": "http://workflow-engine:8010",
            "health_check": "/health",
            "timeout": 30,
            "retry": 3
        },
        "notification-service": {
            "url": "http://notification-service:8011",
            "health_check": "/health",
            "timeout": 30,
            "retry": 3
        },
        "file-service": {
            "url": "http://file-service:8012",
            "health_check": "/health",
            "timeout": 30,
            "retry": 3
        }
    }
    
    # 路由配置
    ROUTE_MAPPINGS: Dict[str, str] = {
        "/api/v1/auth": "mcp-core",
        "/api/v1/users": "mcp-core",
        "/api/v1/roles": "mcp-core",
        "/api/v1/leads": "lead-insight",
        "/api/v1/customers": "lead-insight",
        "/api/v1/contracts": "sales-contract",
        "/api/v1/projects": "project-execution",
        "/api/v1/bom": "bom-supply",
        "/api/v1/supply": "bom-supply",
        "/api/v1/after-sales": "after-sales",
        "/api/v1/finance": "finance-settlement",
        "/api/v1/analytics": "data-analytics",
        "/api/v1/ai": "ai-service",
        "/api/v1/chat": "ai-service",
        "/api/v1/knowledge": "ai-service",
        "/api/v1/workflows": "workflow-engine",
        "/api/v1/notifications": "notification-service",
        "/api/v1/files": "file-service"
    }
    
    # 负载均衡配置
    LOAD_BALANCER_ALGORITHM: str = "round_robin"  # round_robin, least_connections, weighted
    HEALTH_CHECK_INTERVAL: int = 30  # 秒
    HEALTH_CHECK_TIMEOUT: int = 5  # 秒
    
    # 缓存配置
    CACHE_TTL: int = 300  # 5分钟
    CACHE_ENABLED: bool = True
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"
    
    # 监控配置
    METRICS_ENABLED: bool = True
    METRICS_PATH: str = "/metrics"
    
    # 安全配置
    ALLOWED_HOSTS: List[str] = ["*"]
    TRUSTED_PROXIES: List[str] = []
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# 创建全局配置实例
settings = Settings()


def get_service_url(service_name: str) -> Optional[str]:
    """获取服务URL"""
    service_config = settings.BACKEND_SERVICES.get(service_name)
    if service_config:
        return service_config["url"]
    return None


def get_service_for_path(path: str) -> Optional[str]:
    """根据路径获取对应的服务"""
    for route_prefix, service_name in settings.ROUTE_MAPPINGS.items():
        if path.startswith(route_prefix):
            return service_name
    return None
