"""
聊天对话API
"""

from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
import json
from loguru import logger

from ...core.llm import get_llm, LLMManager
from ...core.vector_db import get_vector_db, VectorDBManager

router = APIRouter()


class ChatRequest(BaseModel):
    """聊天请求"""
    message: str
    context: Optional[str] = None
    temperature: Optional[float] = None
    max_tokens: Optional[int] = None
    use_knowledge_base: bool = True
    knowledge_limit: int = 5


class ChatResponse(BaseModel):
    """聊天响应"""
    response: str
    context_used: Optional[str] = None
    knowledge_sources: List[Dict[str, Any]] = []


class StreamChatRequest(BaseModel):
    """流式聊天请求"""
    message: str
    context: Optional[str] = None
    temperature: Optional[float] = None
    use_knowledge_base: bool = True
    knowledge_limit: int = 5


@router.post("/", response_model=ChatResponse)
async def chat(
    request: ChatRequest,
    llm: LLMManager = Depends(get_llm),
    vector_db: VectorDBManager = Depends(get_vector_db)
):
    """聊天对话"""
    try:
        context = request.context
        knowledge_sources = []
        
        # 如果启用知识库搜索
        if request.use_knowledge_base:
            # 搜索相关知识
            similar_docs = await vector_db.search_similar(
                query=request.message,
                limit=request.knowledge_limit,
                score_threshold=0.7
            )
            
            if similar_docs:
                # 构建知识上下文
                knowledge_context = "\n\n".join([
                    f"知识片段 {i+1}：{doc['content']}"
                    for i, doc in enumerate(similar_docs)
                ])
                
                # 合并上下文
                if context:
                    context = f"{context}\n\n相关知识：\n{knowledge_context}"
                else:
                    context = f"相关知识：\n{knowledge_context}"
                
                knowledge_sources = similar_docs
        
        # 生成回复
        response = await llm.generate_response(
            prompt=request.message,
            context=context,
            temperature=request.temperature,
            max_tokens=request.max_tokens
        )
        
        return ChatResponse(
            response=response,
            context_used=context,
            knowledge_sources=knowledge_sources
        )
        
    except Exception as e:
        logger.error(f"Chat error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/stream")
async def stream_chat(
    request: StreamChatRequest,
    llm: LLMManager = Depends(get_llm),
    vector_db: VectorDBManager = Depends(get_vector_db)
):
    """流式聊天对话"""
    try:
        context = request.context
        
        # 如果启用知识库搜索
        if request.use_knowledge_base:
            # 搜索相关知识
            similar_docs = await vector_db.search_similar(
                query=request.message,
                limit=request.knowledge_limit,
                score_threshold=0.7
            )
            
            if similar_docs:
                # 构建知识上下文
                knowledge_context = "\n\n".join([
                    f"知识片段 {i+1}：{doc['content']}"
                    for i, doc in enumerate(similar_docs)
                ])
                
                # 合并上下文
                if context:
                    context = f"{context}\n\n相关知识：\n{knowledge_context}"
                else:
                    context = f"相关知识：\n{knowledge_context}"
        
        async def generate():
            try:
                async for chunk in llm.generate_streaming_response(
                    prompt=request.message,
                    context=context,
                    temperature=request.temperature
                ):
                    yield f"data: {json.dumps({'content': chunk}, ensure_ascii=False)}\n\n"
                
                # 发送结束标记
                yield f"data: {json.dumps({'done': True}, ensure_ascii=False)}\n\n"
                
            except Exception as e:
                logger.error(f"Stream chat error: {e}")
                yield f"data: {json.dumps({'error': str(e)}, ensure_ascii=False)}\n\n"
        
        return StreamingResponse(
            generate(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream"
            }
        )
        
    except Exception as e:
        logger.error(f"Stream chat error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def chat_health():
    """聊天服务健康检查"""
    return {"status": "healthy", "service": "chat"}
