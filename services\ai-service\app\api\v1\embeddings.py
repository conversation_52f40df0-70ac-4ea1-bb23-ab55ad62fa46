"""
向量嵌入API
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Dict, Any
from loguru import logger

from ...core.llm import get_llm, LLMManager
from ...core.vector_db import get_vector_db, VectorDBManager

router = APIRouter()


class EmbeddingRequest(BaseModel):
    """嵌入请求"""
    texts: List[str]


class EmbeddingResponse(BaseModel):
    """嵌入响应"""
    embeddings: List[List[float]]
    dimensions: int
    model: str


class SimilarityRequest(BaseModel):
    """相似度请求"""
    text1: str
    text2: str


class SimilarityResponse(BaseModel):
    """相似度响应"""
    similarity: float
    text1_embedding: List[float]
    text2_embedding: List[float]


@router.post("/generate", response_model=EmbeddingResponse)
async def generate_embeddings(
    request: EmbeddingRequest,
    llm: LLMManager = Depends(get_llm)
):
    """生成文本嵌入向量"""
    try:
        # 生成嵌入向量
        embeddings = await llm.generate_embeddings(request.texts)
        
        if not embeddings:
            raise HTTPException(status_code=500, detail="Failed to generate embeddings")
        
        return EmbeddingResponse(
            embeddings=embeddings,
            dimensions=len(embeddings[0]) if embeddings else 0,
            model="text-embedding-ada-002"
        )
        
    except Exception as e:
        logger.error(f"Failed to generate embeddings: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/similarity", response_model=SimilarityResponse)
async def calculate_similarity(
    request: SimilarityRequest,
    llm: LLMManager = Depends(get_llm)
):
    """计算文本相似度"""
    try:
        # 生成两个文本的嵌入向量
        embeddings = await llm.generate_embeddings([request.text1, request.text2])
        
        if len(embeddings) != 2:
            raise HTTPException(status_code=500, detail="Failed to generate embeddings")
        
        # 计算余弦相似度
        import numpy as np
        
        vec1 = np.array(embeddings[0])
        vec2 = np.array(embeddings[1])
        
        # 余弦相似度计算
        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)
        
        similarity = dot_product / (norm1 * norm2)
        
        return SimilarityResponse(
            similarity=float(similarity),
            text1_embedding=embeddings[0],
            text2_embedding=embeddings[1]
        )
        
    except Exception as e:
        logger.error(f"Failed to calculate similarity: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/search")
async def search_by_embedding(
    embedding: List[float],
    limit: int = 10,
    score_threshold: float = 0.7,
    vector_db: VectorDBManager = Depends(get_vector_db)
):
    """通过嵌入向量搜索"""
    try:
        # 直接使用嵌入向量搜索
        from qdrant_client.http.models import SearchRequest
        
        search_result = vector_db.client.search(
            collection_name=vector_db.collection_name,
            query_vector=embedding,
            limit=limit,
            score_threshold=score_threshold
        )
        
        # 格式化结果
        results = []
        for hit in search_result:
            results.append({
                'id': hit.id,
                'score': hit.score,
                'content': hit.payload.get('content', ''),
                'metadata': hit.payload.get('metadata', {}),
                'source': hit.payload.get('source', ''),
                'timestamp': hit.payload.get('timestamp', '')
            })
        
        return {
            "results": results,
            "total": len(results)
        }
        
    except Exception as e:
        logger.error(f"Failed to search by embedding: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def embeddings_health():
    """嵌入服务健康检查"""
    return {"status": "healthy", "service": "embeddings"}
