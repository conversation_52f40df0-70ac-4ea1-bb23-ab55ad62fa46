"""
大语言模型管理
"""

from typing import List, Dict, Any, Optional, AsyncGenerator
import asyncio
from openai import AsyncOpenAI
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document
import tiktoken
from loguru import logger

from .config import settings


class LLMManager:
    """大语言模型管理器"""
    
    def __init__(self):
        self.client: Optional[AsyncOpenAI] = None
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=settings.CHUNK_SIZE,
            chunk_overlap=settings.CHUNK_OVERLAP
        )
        self.encoding = tiktoken.encoding_for_model("gpt-4")
        
    async def initialize(self):
        """初始化大语言模型"""
        try:
            if settings.OPENAI_API_KEY:
                self.client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
                logger.info("OpenAI client initialized successfully")
            else:
                logger.warning("OpenAI API key not provided")
                
        except Exception as e:
            logger.error(f"Failed to initialize LLM: {e}")
            raise
    
    async def generate_response(self, prompt: str, context: Optional[str] = None,
                              temperature: float = None, max_tokens: int = None) -> str:
        """生成回复"""
        try:
            if not self.client:
                raise ValueError("LLM client not initialized")
            
            # 构建消息
            messages = []
            
            if context:
                messages.append({
                    "role": "system",
                    "content": f"基于以下上下文信息回答问题：\n\n{context}"
                })
            
            messages.append({
                "role": "user",
                "content": prompt
            })
            
            # 调用OpenAI API
            response = await self.client.chat.completions.create(
                model=settings.OPENAI_MODEL,
                messages=messages,
                temperature=temperature or settings.OPENAI_TEMPERATURE,
                max_tokens=max_tokens or settings.OPENAI_MAX_TOKENS
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"Failed to generate response: {e}")
            raise
    
    async def generate_streaming_response(self, prompt: str, context: Optional[str] = None,
                                        temperature: float = None) -> AsyncGenerator[str, None]:
        """生成流式回复"""
        try:
            if not self.client:
                raise ValueError("LLM client not initialized")
            
            # 构建消息
            messages = []
            
            if context:
                messages.append({
                    "role": "system",
                    "content": f"基于以下上下文信息回答问题：\n\n{context}"
                })
            
            messages.append({
                "role": "user",
                "content": prompt
            })
            
            # 调用OpenAI API（流式）
            stream = await self.client.chat.completions.create(
                model=settings.OPENAI_MODEL,
                messages=messages,
                temperature=temperature or settings.OPENAI_TEMPERATURE,
                stream=True
            )
            
            async for chunk in stream:
                if chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            logger.error(f"Failed to generate streaming response: {e}")
            raise
    
    async def extract_entities(self, text: str) -> Dict[str, List[str]]:
        """提取实体"""
        try:
            prompt = f"""
            从以下文本中提取关键实体，按类别分组：
            
            文本：{text}
            
            请提取以下类别的实体：
            - 人名
            - 公司名
            - 产品名
            - 地点
            - 时间
            - 金额
            
            以JSON格式返回结果。
            """
            
            response = await self.generate_response(prompt)
            
            # 这里应该解析JSON响应，简化处理
            import json
            try:
                entities = json.loads(response)
                return entities
            except:
                return {}
                
        except Exception as e:
            logger.error(f"Failed to extract entities: {e}")
            return {}
    
    async def summarize_text(self, text: str, max_length: int = 200) -> str:
        """文本摘要"""
        try:
            prompt = f"""
            请为以下文本生成一个简洁的摘要，长度不超过{max_length}字：
            
            {text}
            
            摘要：
            """
            
            response = await self.generate_response(prompt, max_tokens=max_length)
            return response.strip()
            
        except Exception as e:
            logger.error(f"Failed to summarize text: {e}")
            return ""
    
    async def classify_text(self, text: str, categories: List[str]) -> Dict[str, float]:
        """文本分类"""
        try:
            categories_str = "、".join(categories)
            prompt = f"""
            请将以下文本分类到这些类别中：{categories_str}
            
            文本：{text}
            
            请为每个类别给出0-1之间的置信度分数，以JSON格式返回。
            """
            
            response = await self.generate_response(prompt)
            
            # 解析分类结果
            import json
            try:
                classification = json.loads(response)
                return classification
            except:
                return {cat: 0.0 for cat in categories}
                
        except Exception as e:
            logger.error(f"Failed to classify text: {e}")
            return {cat: 0.0 for cat in categories}
    
    def split_text(self, text: str) -> List[Document]:
        """分割文本"""
        try:
            documents = self.text_splitter.create_documents([text])
            return documents
        except Exception as e:
            logger.error(f"Failed to split text: {e}")
            return []
    
    def count_tokens(self, text: str) -> int:
        """计算token数量"""
        try:
            return len(self.encoding.encode(text))
        except Exception as e:
            logger.error(f"Failed to count tokens: {e}")
            return 0
    
    async def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """生成嵌入向量"""
        try:
            if not self.client:
                raise ValueError("LLM client not initialized")
            
            response = await self.client.embeddings.create(
                model="text-embedding-ada-002",
                input=texts
            )
            
            return [data.embedding for data in response.data]
            
        except Exception as e:
            logger.error(f"Failed to generate embeddings: {e}")
            return []


# 全局LLM管理器实例
llm_manager = LLMManager()


async def init_llm():
    """初始化大语言模型"""
    await llm_manager.initialize()


def get_llm() -> LLMManager:
    """获取LLM管理器"""
    return llm_manager
